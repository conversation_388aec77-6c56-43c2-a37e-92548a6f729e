{"rustc": 1842507548689473721, "features": "[\"bytes\", \"compression\", \"custom-protocol\", \"default\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"objc-exception\", \"open\", \"regex\", \"reqwest\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-min-size\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 16287072401622553093, "path": 2762511390010927866, "deps": [[40386456601120721, "percent_encoding", false, 2291806702945082503], [1260461579271933187, "serialize_to_javascript", false, 7294399170575306872], [1441306149310335789, "tempfile", false, 5271604083319591601], [3150220818285335163, "url", false, 17738334534580791688], [3722963349756955755, "once_cell", false, 11070729840169075969], [3988549704697787137, "open", false, 156638409942716097], [4381063397040571828, "webview2_com", false, 16953730341895750684], [4405182208873388884, "http", false, 17372756678028960194], [4450062412064442726, "dirs_next", false, 5190832854481697489], [4899080583175475170, "semver", false, 9495104336160992446], [5180608563399064494, "tauri_macros", false, 12521688749485850299], [5610773616282026064, "build_script_build", false, 15453503970247851459], [5986029879202738730, "log", false, 8296542160911402746], [7244058819997729774, "reqwest", false, 8517407510941878614], [7653476968652377684, "windows", false, 3449051565116303854], [8008191657135824715, "thiserror", false, 13516774549906574380], [8292277814562636972, "tauri_utils", false, 12153133319836338637], [8319709847752024821, "uuid", false, 7890530018446933976], [9451456094439810778, "regex", false, 6537062778576329035], [9623796893764309825, "ignore", false, 9745667391754869801], [9689903380558560274, "serde", false, 3711165212804299083], [9920160576179037441, "getrandom", false, 9732769722631103888], [10629569228670356391, "futures_util", false, 9439213555665817681], [11601763207901161556, "tar", false, 12184100685733781846], [11693073011723388840, "raw_window_handle", false, 7740374353353793669], [11989259058781683633, "dunce", false, 2758556744653672316], [12393800526703971956, "tokio", false, 14321819318010974058], [12986574360607194341, "serde_repr", false, 13336527183423025279], [13208667028893622512, "rand", false, 4007877964893673928], [13625485746686963219, "anyhow", false, 4276441953790888153], [14162324460024849578, "tauri_runtime", false, 14455613151857842315], [14564311161534545801, "encoding_rs", false, 3849335528499029362], [14923790796823607459, "indexmap", false, 6910433704571151769], [15367738274754116744, "serde_json", false, 319892754931724390], [16066129441945555748, "bytes", false, 3944688214238992537], [16228250612241359704, "tauri_runtime_wry", false, 17197237316399263547], [17155886227862585100, "glob", false, 3386706419477064543], [17278893514130263345, "state", false, 6773235408762650015], [17772299992546037086, "flate2", false, 14487276017379478685]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-e82dc4382e1b41ff\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}