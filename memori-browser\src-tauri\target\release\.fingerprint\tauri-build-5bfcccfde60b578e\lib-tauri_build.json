{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 13390825267576250845, "path": 21500578101221346, "deps": [[4450062412064442726, "dirs_next", false, 6194265933918663728], [4899080583175475170, "semver", false, 1549203043095575051], [7468248713591957673, "cargo_toml", false, 15298358060554010834], [8292277814562636972, "tauri_utils", false, 312666523984760491], [9689903380558560274, "serde", false, 11335753460197236072], [10301936376833819828, "json_patch", false, 9639229409941567450], [13077543566650298139, "heck", false, 15546109870073052242], [13625485746686963219, "anyhow", false, 10088971677172219974], [14189313126492979171, "tauri_winres", false, 11225080873371525981], [15367738274754116744, "serde_json", false, 17428953512726854418], [15622660310229662834, "walkdir", false, 9503943136891004439]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-5bfcccfde60b578e\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}