﻿import React, { useState, useEffect } from "react";
import { Plus, X, Brain, Zap, Globe, Shield, Clock, Star, TrendingUp } from "lucide-react";

export function TabBar() {
    const [tabs, setTabs] = useState([
        { id: 1, title: "AI Research Hub", url: "https://research.memori.ai", isActive: true, isLoading: false, aiScore: 95, favicon: "" },
        { id: 2, title: "GitHub - React Projects", url: "https://github.com", isActive: false, isLoading: false, aiScore: 88, favicon: "" },
        { id: 3, title: "OpenAI Documentation", url: "https://docs.openai.com", isActive: false, isLoading: true, aiScore: 92, favicon: "" }
    ]);

    const [aiSuggestions, setAiSuggestions] = useState([
        "AI suggests grouping these tabs by topic",
        "Similar content found in your browsing history",
        "Auto-save this research session?"
    ]);

    const addNewTab = () => {
        const newTab = {
            id: Date.now(),
            title: "New Tab",
            url: "memori://newtab",
            isActive: true,
            isLoading: false,
            aiScore: 0,
            favicon: ""
        };
        setTabs(tabs.map(tab => ({ ...tab, isActive: false })).concat(newTab));
    };

    const closeTab = (tabId) => {
        setTabs(tabs.filter(tab => tab.id !== tabId));
    };

    const switchTab = (tabId) => {
        setTabs(tabs.map(tab => ({ ...tab, isActive: tab.id === tabId })));
    };

    return (
        <div className="bg-gray-800 border-b border-gray-700">
            <div className="flex items-center justify-between px-4 py-2">
                <div className="flex items-center gap-2 flex-1 overflow-x-auto">
                    {tabs.map((tab) => (
                        <div
                            key={tab.id}
                            className={`group flex items-center gap-2 px-4 py-2 rounded-t-lg min-w-0 max-w-xs cursor-pointer transition-all duration-200 ${
                                tab.isActive 
                                    ? "bg-gray-700 border-b-2 border-blue-500" 
                                    : "bg-gray-800 hover:bg-gray-700"
                            }`}
                            onClick={() => switchTab(tab.id)}
                        >
                            <span className="text-lg">{tab.favicon}</span>
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                    <span className="text-white text-sm truncate">{tab.title}</span>
                                    {tab.isLoading && (
                                        <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-400"></div>
                                    )}
                                </div>
                                <div className="flex items-center gap-1 mt-1">
                                    <Shield className="w-3 h-3 text-green-400" />
                                    <span className="text-xs text-gray-400">{tab.url.split("//")[1]?.split("/")[0]}</span>
                                    {tab.aiScore > 0 && (
                                        <div className="flex items-center gap-1 ml-2">
                                            <Brain className="w-3 h-3 text-purple-400" />
                                            <span className="text-xs text-purple-400">{tab.aiScore}%</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    closeTab(tab.id);
                                }}
                                className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-600 rounded transition-all"
                            >
                                <X className="w-3 h-3 text-gray-400" />
                            </button>
                        </div>
                    ))}
                    
                    <button
                        onClick={addNewTab}
                        className="flex items-center justify-center w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded transition-colors"
                    >
                        <Plus className="w-4 h-4 text-gray-400" />
                    </button>
                </div>

                <div className="flex items-center gap-4 ml-4">
                    <div className="flex items-center gap-2 px-3 py-1 bg-gradient-to-r from-purple-900 to-blue-900 rounded-full">
                        <Brain className="w-4 h-4 text-purple-400" />
                        <span className="text-xs text-purple-300">AI Active</span>
                    </div>
                    
                    <div className="flex items-center gap-2 px-3 py-1 bg-gray-700 rounded-full">
                        <TrendingUp className="w-4 h-4 text-green-400" />
                        <span className="text-xs text-green-300">3 insights</span>
                    </div>
                </div>
            </div>

            {/* AI Suggestions Bar */}
            <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-t border-purple-500/20 px-4 py-2">
                <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-purple-400" />
                        <span className="text-purple-300 font-medium">AI Insights:</span>
                    </div>
                    <div className="flex items-center gap-6 overflow-x-auto">
                        {aiSuggestions.map((suggestion, index) => (
                            <button
                                key={index}
                                className="text-gray-300 hover:text-white transition-colors whitespace-nowrap"
                            >
                                {suggestion}
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}
