{"rustc": 1842507548689473721, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 15657897354478470176, "path": 6552554074254449872, "deps": [[784494742817713399, "tower_service", false, 15314339490031985925], [1569313478171189446, "want", false, 10504870678739858294], [1811549171721445101, "futures_channel", false, 6247408772680830837], [1906322745568073236, "pin_project_lite", false, 16371255440629486796], [4405182208873388884, "http", false, 9858713576818919528], [6163892036024256188, "httparse", false, 14918455716676836902], [6304235478050270880, "httpdate", false, 17332148666222625845], [7620660491849607393, "futures_core", false, 235090336762385746], [7695812897323945497, "itoa", false, 1430850237883331419], [8606274917505247608, "tracing", false, 990696502168126368], [8915503303801890683, "http_body", false, 1238682007532359361], [10629569228670356391, "futures_util", false, 5134276775627054024], [12393800526703971956, "tokio", false, 9721431931746701121], [12614995553916589825, "socket2", false, 9566060886136621941], [13763625454224483636, "h2", false, 760624058737947093], [16066129441945555748, "bytes", false, 14873694996098673855]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-448ae013abe89610\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}