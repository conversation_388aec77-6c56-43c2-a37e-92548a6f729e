{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 16287072401622553093, "path": 112649233362061231, "deps": [[3150220818285335163, "url", false, 17738334534580791688], [4381063397040571828, "webview2_com", false, 16953730341895750684], [4405182208873388884, "http", false, 17372756678028960194], [7653476968652377684, "windows", false, 3449051565116303854], [8008191657135824715, "thiserror", false, 13516774549906574380], [8292277814562636972, "tauri_utils", false, 12153133319836338637], [8319709847752024821, "uuid", false, 7890530018446933976], [8866577183823226611, "http_range", false, 4484555030232248624], [9689903380558560274, "serde", false, 3711165212804299083], [11693073011723388840, "raw_window_handle", false, 7740374353353793669], [13208667028893622512, "rand", false, 4007877964893673928], [14162324460024849578, "build_script_build", false, 14075089185743380742], [15367738274754116744, "serde_json", false, 319892754931724390]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-7e8ce43a696c3ef2\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}