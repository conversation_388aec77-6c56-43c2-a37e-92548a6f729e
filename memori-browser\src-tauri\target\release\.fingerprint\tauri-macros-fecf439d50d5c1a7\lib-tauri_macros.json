{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 11774737094483147028, "deps": [[2713742371683562785, "syn", false, 14543897139544724538], [3060637413840920116, "proc_macro2", false, 10231316123642883689], [8292277814562636972, "tauri_utils", false, 312666523984760491], [13077543566650298139, "heck", false, 15546109870073052242], [17492769205600034078, "tauri_codegen", false, 15854848809280188528], [17990358020177143287, "quote", false, 18104525677915743884]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-fecf439d50d5c1a7\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}