{"rustc": 1842507548689473721, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 2235758518937167739, "deps": [[40386456601120721, "percent_encoding", false, 14044020114199803101], [95042085696191081, "ipnet", false, 685514092924317127], [264090853244900308, "sync_wrapper", false, 5284879396755889268], [784494742817713399, "tower_service", false, 15314339490031985925], [1288403060204016458, "tokio_util", false, 7140234741303155264], [1906322745568073236, "pin_project_lite", false, 16371255440629486796], [3150220818285335163, "url", false, 16680178351721664748], [3722963349756955755, "once_cell", false, 6551543648015782790], [4405182208873388884, "http", false, 9858713576818919528], [5986029879202738730, "log", false, 15103776660954145007], [7414427314941361239, "hyper", false, 9741741860991755585], [7620660491849607393, "futures_core", false, 235090336762385746], [8405603588346937335, "winreg", false, 268726696540934857], [8915503303801890683, "http_body", false, 1238682007532359361], [9689903380558560274, "serde", false, 1218481086981197372], [10229185211513642314, "mime", false, 6474388061583809043], [10629569228670356391, "futures_util", false, 5134276775627054024], [12186126227181294540, "tokio_native_tls", false, 9859653231224686751], [12367227501898450486, "hyper_tls", false, 12670698584918153908], [12393800526703971956, "tokio", false, 9721431931746701121], [13763625454224483636, "h2", false, 760624058737947093], [14564311161534545801, "encoding_rs", false, 5633263186074840385], [15367738274754116744, "serde_json", false, 2855758561636807193], [16066129441945555748, "bytes", false, 14873694996098673855], [16311359161338405624, "rustls_pemfile", false, 2190328032465142827], [16542808166767769916, "serde_urlencoded", false, 7963113489690763621], [16785601910559813697, "native_tls_crate", false, 14133480192954776467], [18066890886671768183, "base64", false, 10447348104625118583]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-4bc1f33d9b07a4dd\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}