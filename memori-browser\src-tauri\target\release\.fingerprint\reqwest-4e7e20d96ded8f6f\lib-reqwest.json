{"rustc": 1842507548689473721, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 2235758518937167739, "deps": [[40386456601120721, "percent_encoding", false, 2291806702945082503], [95042085696191081, "ipnet", false, 16000899138422703924], [264090853244900308, "sync_wrapper", false, 1430344218649247206], [784494742817713399, "tower_service", false, 6989258484209530198], [1288403060204016458, "tokio_util", false, 12035031334041095763], [1906322745568073236, "pin_project_lite", false, 15888961290013833884], [3150220818285335163, "url", false, 17738334534580791688], [3722963349756955755, "once_cell", false, 11070729840169075969], [4405182208873388884, "http", false, 17372756678028960194], [5986029879202738730, "log", false, 8296542160911402746], [7414427314941361239, "hyper", false, 7892337445536938761], [7620660491849607393, "futures_core", false, 7888856330301198808], [8405603588346937335, "winreg", false, 14118474085653503311], [8915503303801890683, "http_body", false, 11725070852734198485], [9689903380558560274, "serde", false, 3711165212804299083], [10229185211513642314, "mime", false, 3564309220070663013], [10629569228670356391, "futures_util", false, 9439213555665817681], [12186126227181294540, "tokio_native_tls", false, 12628462199287449679], [12367227501898450486, "hyper_tls", false, 12480602344089839814], [12393800526703971956, "tokio", false, 14321819318010974058], [13763625454224483636, "h2", false, 5734785204974090993], [14564311161534545801, "encoding_rs", false, 3849335528499029362], [15367738274754116744, "serde_json", false, 319892754931724390], [16066129441945555748, "bytes", false, 3944688214238992537], [16311359161338405624, "rustls_pemfile", false, 6277320250129352995], [16542808166767769916, "serde_urlencoded", false, 16112933024837671064], [16785601910559813697, "native_tls_crate", false, 11987216949862972082], [18066890886671768183, "base64", false, 5176544414896839272]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-4e7e20d96ded8f6f\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}