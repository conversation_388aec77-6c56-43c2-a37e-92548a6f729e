{"build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "devPath": "http://localhost:1420", "distDir": "../dist"}, "package": {"productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "create": true, "center": true, "requestUserAttention": true, "setResizable": true, "setTitle": true, "maximize": true, "unmaximize": true, "minimize": true, "unminimize": true, "show": true, "hide": true, "close": true, "setDecorations": true, "setAlwaysOnTop": true, "setSize": true, "setMinSize": true, "setMaxSize": true, "setPosition": true, "setFullscreen": true, "setFocus": true, "setIcon": true, "setSkipTaskbar": true, "setCursorGrab": true, "setCursorVisible": true, "setCursorIcon": true, "setCursorPosition": true, "setIgnoreCursorEvents": true, "startDragging": true, "print": true}, "http": {"all": true, "request": true}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.memori.browser", "longDescription": "", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": [], "shortDescription": "", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": "default-src 'self'; connect-src 'self' https: http: ws: wss:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https: http:; font-src 'self' https: data:; frame-src 'self' https: http:;"}, "updater": {"active": false}, "windows": [{"fullscreen": false, "height": 900, "resizable": true, "title": "<PERSON><PERSON><PERSON>", "width": 1400, "minWidth": 1000, "minHeight": 700}]}}