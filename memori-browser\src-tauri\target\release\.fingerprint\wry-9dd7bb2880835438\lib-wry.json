{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2040997289075261528, "path": 14528530421717216679, "deps": [[3007252114546291461, "tao", false, 1875312997705562246], [3150220818285335163, "url", false, 17738334534580791688], [3540822385484940109, "windows_implement", false, 13563659603470663559], [3722963349756955755, "once_cell", false, 11070729840169075969], [4381063397040571828, "webview2_com", false, 16953730341895750684], [4405182208873388884, "http", false, 17372756678028960194], [4684437522915235464, "libc", false, 2946486472956055370], [5986029879202738730, "log", false, 8296542160911402746], [7653476968652377684, "windows", false, 3449051565116303854], [8008191657135824715, "thiserror", false, 13516774549906574380], [8391357152270261188, "build_script_build", false, 1411280681132623137], [9689903380558560274, "serde", false, 3711165212804299083], [11989259058781683633, "dunce", false, 2758556744653672316], [15367738274754116744, "serde_json", false, 319892754931724390]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-9dd7bb2880835438\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}