{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 10956804577811572050, "deps": [[4381063397040571828, "webview2_com", false, 16953730341895750684], [7653476968652377684, "windows", false, 3449051565116303854], [8292277814562636972, "tauri_utils", false, 12153133319836338637], [8319709847752024821, "uuid", false, 7890530018446933976], [8391357152270261188, "wry", false, 13675186879053770272], [11693073011723388840, "raw_window_handle", false, 7740374353353793669], [13208667028893622512, "rand", false, 4007877964893673928], [14162324460024849578, "tauri_runtime", false, 14455613151857842315], [16228250612241359704, "build_script_build", false, 16871379189929664930]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-d9bb0053ed821b29\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}