// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{Manager, command};

mod browser_engine;

use browser_engine::BrowserEngine;

#[command]
async fn open_url(url: String) -> Result<(), String> {
    println!("Opening URL: {}", url);

    // Open URL in system default browser
    match open::that(&url) {
        Ok(_) => {
            println!("Successfully opened URL: {}", url);
            Ok(())
        }
        Err(e) => {
            eprintln!("Failed to open URL {}: {}", url, e);
            Err(format!("Failed to open URL: {}", e))
        }
    }
}

fn main() {
    tauri::Builder::default()
        .setup(|app| {
            let handle = app.handle();

            // Initialize browser engine
            let browser_engine: Arc<BrowserEngine<tauri::Wry>> = Arc::new(BrowserEngine::new(handle.clone()));
            app.manage(browser_engine);

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            open_url,
            browser_engine::create_tab,
            browser_engine::navigate_to,
            browser_engine::go_back,
            browser_engine::go_forward,
            browser_engine::refresh_tab,
            browser_engine::close_tab,
            browser_engine::get_all_tabs,
            browser_engine::get_tab,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
