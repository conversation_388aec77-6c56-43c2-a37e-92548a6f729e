// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::command;

#[command]
async fn open_url(url: String) -> Result<(), String> {
    println!("Opening URL: {}", url);

    // Open URL in system default browser
    match open::that(&url) {
        Ok(_) => {
            println!("Successfully opened URL: {}", url);
            Ok(())
        }
        Err(e) => {
            eprintln!("Failed to open URL {}: {}", url, e);
            Err(format!("Failed to open URL: {}", e))
        }
    }
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![open_url])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
