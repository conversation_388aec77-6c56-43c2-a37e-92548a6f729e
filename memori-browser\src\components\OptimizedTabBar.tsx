import React, { useState, useRef, useEffect } from 'react';
import { X, Plus, MoreHorizontal } from 'lucide-react';
import { useBrowserStore } from '../stores/browserStore';

interface TabProps {
  tab: {
    id: string;
    title: string;
    url: string;
    isLoading: boolean;
    favicon?: string;
  };
  isActive: boolean;
  onSelect: () => void;
  onClose: () => void;
}

const Tab: React.FC<TabProps> = ({ tab, isActive, onSelect, onClose }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  const getDisplayTitle = (title: string, url: string) => {
    if (title && title !== 'New Tab') {
      return title.length > 20 ? title.substring(0, 20) + '...' : title;
    }
    
    try {
      const urlObj = new URL(url);
      return urlObj.hostname || url;
    } catch {
      return url.length > 20 ? url.substring(0, 20) + '...' : url;
    }
  };

  const getFavicon = (url: string, favicon?: string) => {
    if (favicon) return favicon;
    
    try {
      const urlObj = new URL(url);
      return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`;
    } catch {
      return null;
    }
  };

  return (
    <div
      className={`
        flex items-center gap-2 px-3 py-2 min-w-0 max-w-[200px] cursor-pointer
        border-r border-gray-200 dark:border-gray-700 transition-all duration-150
        ${isActive 
          ? 'bg-white dark:bg-gray-800 border-b-2 border-b-blue-500' 
          : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
        }
      `}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Favicon */}
      <div className="flex-shrink-0 w-4 h-4">
        {tab.isLoading ? (
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        ) : (
          <img
            src={getFavicon(tab.url, tab.favicon)}
            alt=""
            className="w-4 h-4"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        )}
      </div>

      {/* Title */}
      <span className="flex-1 min-w-0 text-sm text-gray-700 dark:text-gray-300 truncate">
        {getDisplayTitle(tab.title, tab.url)}
      </span>

      {/* Close button */}
      {(isHovered || isActive) && (
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          title="Close tab"
        >
          <X className="w-3 h-3 text-gray-500 dark:text-gray-400" />
        </button>
      )}
    </div>
  );
};

const OptimizedTabBar: React.FC = () => {
  const { tabs, activeTabId, createTab, closeTab, setActiveTab } = useBrowserStore();
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkScrollable = () => {
      if (tabsContainerRef.current) {
        const { scrollWidth, clientWidth } = tabsContainerRef.current;
        setShowScrollButtons(scrollWidth > clientWidth);
      }
    };

    checkScrollable();
    window.addEventListener('resize', checkScrollable);
    return () => window.removeEventListener('resize', checkScrollable);
  }, [tabs]);

  const scrollTabs = (direction: 'left' | 'right') => {
    if (tabsContainerRef.current) {
      const scrollAmount = 200;
      const newScrollLeft = tabsContainerRef.current.scrollLeft + 
        (direction === 'right' ? scrollAmount : -scrollAmount);
      
      tabsContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  const handleNewTab = () => {
    createTab();
  };

  const handleTabSelect = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleTabClose = (tabId: string) => {
    closeTab(tabId);
  };

  return (
    <div className="flex items-center bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      {/* Scroll left button */}
      {showScrollButtons && (
        <button
          onClick={() => scrollTabs('left')}
          className="flex-shrink-0 p-2 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          title="Scroll tabs left"
        >
          <MoreHorizontal className="w-4 h-4 text-gray-600 dark:text-gray-400 rotate-90" />
        </button>
      )}

      {/* Tabs container */}
      <div
        ref={tabsContainerRef}
        className="flex-1 flex overflow-x-auto scrollbar-hide"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.id}
            tab={tab}
            isActive={tab.id === activeTabId}
            onSelect={() => handleTabSelect(tab.id)}
            onClose={() => handleTabClose(tab.id)}
          />
        ))}
      </div>

      {/* Scroll right button */}
      {showScrollButtons && (
        <button
          onClick={() => scrollTabs('right')}
          className="flex-shrink-0 p-2 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          title="Scroll tabs right"
        >
          <MoreHorizontal className="w-4 h-4 text-gray-600 dark:text-gray-400 rotate-90" />
        </button>
      )}

      {/* New tab button */}
      <button
        onClick={handleNewTab}
        className="flex-shrink-0 p-2 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors border-l border-gray-200 dark:border-gray-700"
        title="New tab"
      >
        <Plus className="w-4 h-4 text-gray-600 dark:text-gray-400" />
      </button>
    </div>
  );
};

export default OptimizedTabBar;
