{"version": 3, "sources": ["../../@tauri-apps/api/helpers/tauri.js", "../../@tauri-apps/api/helpers/event.js", "../../@tauri-apps/api/event.js"], "sourcesContent": ["import { invoke } from '../tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** @ignore */\nasync function invokeTauriCommand(command) {\n    return invoke('tauri', command);\n}\n\nexport { invokeTauriCommand };\n", "import { invokeTauriCommand } from './tauri.js';\nimport { transformCallback } from '../tauri.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Unregister the event listener associated with the given name and id.\n *\n * @ignore\n * @param event The event name\n * @param eventId Event identifier\n * @returns\n */\nasync function _unlisten(event, eventId) {\n    return invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'unlisten',\n            event,\n            eventId\n        }\n    });\n}\n/**\n * Emits an event to the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param [windowLabel] The label of the window to which the event is sent, if null/undefined the event will be sent to all windows\n * @param [payload] Event payload\n * @returns\n */\nasync function emit(event, windowLabel, payload) {\n    await invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'emit',\n            event,\n            windowLabel,\n            payload\n        }\n    });\n}\n/**\n * Listen to an event from the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @return A promise resolving to a function to unlisten to the event.\n */\nasync function listen(event, windowLabel, handler) {\n    return invokeTauriCommand({\n        __tauriModule: 'Event',\n        message: {\n            cmd: 'listen',\n            event,\n            windowLabel,\n            handler: transformCallback(handler)\n        }\n    }).then((eventId) => {\n        return async () => _unlisten(event, eventId);\n    });\n}\n/**\n * Listen to an one-off event from the backend.\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @returns A promise resolving to a function to unlisten to the event.\n */\nasync function once(event, windowLabel, handler) {\n    return listen(event, windowLabel, (eventData) => {\n        handler(eventData);\n        _unlisten(event, eventData.id).catch(() => { });\n    });\n}\n\nexport { emit, listen, once };\n", "import { listen as listen$1, once as once$1, emit as emit$1 } from './helpers/event.js';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The event system allows you to emit events to the backend and listen to events from it.\n *\n * This package is also accessible with `window.__TAURI__.event` when [`build.withGlobalTauri`](https://tauri.app/v1/api/config/#buildconfig.withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * @since 1.1.0\n */\nvar TauriEvent;\n(function (TauriEvent) {\n    TauriEvent[\"WINDOW_RESIZED\"] = \"tauri://resize\";\n    TauriEvent[\"WINDOW_MOVED\"] = \"tauri://move\";\n    TauriEvent[\"WINDOW_CLOSE_REQUESTED\"] = \"tauri://close-requested\";\n    TauriEvent[\"WINDOW_CREATED\"] = \"tauri://window-created\";\n    TauriEvent[\"WINDOW_DESTROYED\"] = \"tauri://destroyed\";\n    TauriEvent[\"WINDOW_FOCUS\"] = \"tauri://focus\";\n    TauriEvent[\"WINDOW_BLUR\"] = \"tauri://blur\";\n    TauriEvent[\"WINDOW_SCALE_FACTOR_CHANGED\"] = \"tauri://scale-change\";\n    TauriEvent[\"WINDOW_THEME_CHANGED\"] = \"tauri://theme-changed\";\n    TauriEvent[\"WINDOW_FILE_DROP\"] = \"tauri://file-drop\";\n    TauriEvent[\"WINDOW_FILE_DROP_HOVER\"] = \"tauri://file-drop-hover\";\n    TauriEvent[\"WINDOW_FILE_DROP_CANCELLED\"] = \"tauri://file-drop-cancelled\";\n    TauriEvent[\"MENU\"] = \"tauri://menu\";\n    TauriEvent[\"CHECK_UPDATE\"] = \"tauri://update\";\n    TauriEvent[\"UPDATE_AVAILABLE\"] = \"tauri://update-available\";\n    TauriEvent[\"INSTALL_UPDATE\"] = \"tauri://update-install\";\n    TauriEvent[\"STATUS_UPDATE\"] = \"tauri://update-status\";\n    TauriEvent[\"DOWNLOAD_PROGRESS\"] = \"tauri://update-download-progress\";\n})(TauriEvent || (TauriEvent = {}));\n/**\n * Listen to an event. The event can be either global or window-specific.\n * See {@link Event.windowLabel} to check the event source.\n *\n * @example\n * ```typescript\n * import { listen } from '@tauri-apps/api/event';\n * const unlisten = await listen<string>('error', (event) => {\n *   console.log(`Got error in window ${event.windowLabel}, payload: ${event.payload}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function listen(event, handler) {\n    return listen$1(event, null, handler);\n}\n/**\n * Listen to an one-off event. See {@link listen} for more information.\n *\n * @example\n * ```typescript\n * import { once } from '@tauri-apps/api/event';\n * interface LoadedPayload {\n *   loggedIn: boolean,\n *   token: string\n * }\n * const unlisten = await once<LoadedPayload>('loaded', (event) => {\n *   console.log(`App is loaded, loggedIn: ${event.payload.loggedIn}, token: ${event.payload.token}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function once(event, handler) {\n    return once$1(event, null, handler);\n}\n/**\n * Emits an event to the backend and all Tauri windows.\n * @example\n * ```typescript\n * import { emit } from '@tauri-apps/api/event';\n * await emit('frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n *\n * @since 1.0.0\n */\nasync function emit(event, payload) {\n    return emit$1(event, undefined, payload);\n}\n\nexport { TauriEvent, emit, listen, once };\n"], "mappings": ";;;;;;;AAMA,eAAe,mBAAmB,SAAS;AACvC,SAAO,OAAO,SAAS,OAAO;AAClC;;;ACMA,eAAe,UAAU,OAAO,SAAS;AACrC,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AASA,eAAe,KAAK,OAAO,aAAa,SAAS;AAC7C,QAAM,mBAAmB;AAAA,IACrB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAQA,eAAe,OAAO,OAAO,aAAa,SAAS;AAC/C,SAAO,mBAAmB;AAAA,IACtB,eAAe;AAAA,IACf,SAAS;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS,kBAAkB,OAAO;AAAA,IACtC;AAAA,EACJ,CAAC,EAAE,KAAK,CAAC,YAAY;AACjB,WAAO,YAAY,UAAU,OAAO,OAAO;AAAA,EAC/C,CAAC;AACL;AAQA,eAAe,KAAK,OAAO,aAAa,SAAS;AAC7C,SAAO,OAAO,OAAO,aAAa,CAAC,cAAc;AAC7C,YAAQ,SAAS;AACjB,cAAU,OAAO,UAAU,EAAE,EAAE,MAAM,MAAM;AAAA,IAAE,CAAC;AAAA,EAClD,CAAC;AACL;;;AC7DA,IAAI;AAAA,CACH,SAAUA,aAAY;AACnB,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,6BAA6B,IAAI;AAC5C,EAAAA,YAAW,sBAAsB,IAAI;AACrC,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,4BAA4B,IAAI;AAC3C,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,eAAe,IAAI;AAC9B,EAAAA,YAAW,mBAAmB,IAAI;AACtC,GAAG,eAAe,aAAa,CAAC,EAAE;AAuBlC,eAAeC,QAAO,OAAO,SAAS;AAClC,SAAO,OAAS,OAAO,MAAM,OAAO;AACxC;AAyBA,eAAeC,MAAK,OAAO,SAAS;AAChC,SAAO,KAAO,OAAO,MAAM,OAAO;AACtC;AAaA,eAAeC,MAAK,OAAO,SAAS;AAChC,SAAO,KAAO,OAAO,QAAW,OAAO;AAC3C;", "names": ["TauriEvent", "listen", "once", "emit"]}