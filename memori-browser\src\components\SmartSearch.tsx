import React, { useState, useRef, useEffect } from 'react';
import { Search, Globe, Clock, Star, Zap } from 'lucide-react';
import { useBrowserStore } from '../stores/browserStore';

interface SearchSuggestion {
  id: string;
  type: 'url' | 'search' | 'history' | 'bookmark' | 'ai';
  title: string;
  url?: string;
  description?: string;
  icon: React.ReactNode;
}

export const SmartSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { activeTabId, navigateTab, createTab, tabs, bookmarks } = useBrowserStore();

  const isValidUrl = (text: string): boolean => {
    try {
      new URL(text.startsWith('http') ? text : `https://${text}`);
      return text.includes('.');
    } catch {
      return false;
    }
  };

  const generateSuggestions = (query: string): SearchSuggestion[] => {
    if (!query.trim()) return [];

    const suggestions: SearchSuggestion[] = [];
    const lowerQuery = query.toLowerCase();

    // Direct URL suggestion
    if (isValidUrl(query)) {
      const url = query.startsWith('http') ? query : `https://${query}`;
      suggestions.push({
        id: 'direct-url',
        type: 'url',
        title: url,
        url,
        description: 'Go to this URL',
        icon: <Globe className="w-4 h-4 text-blue-500" />,
      });
    }

    // Search suggestion
    suggestions.push({
      id: 'search',
      type: 'search',
      title: `Search for "${query}"`,
      url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      description: 'Search on Google',
      icon: <Search className="w-4 h-4 text-green-500" />,
    });

    // Bookmark suggestions
    const matchingBookmarks = bookmarks
      .filter(bookmark => 
        bookmark.title.toLowerCase().includes(lowerQuery) ||
        bookmark.url.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 3);

    matchingBookmarks.forEach(bookmark => {
      suggestions.push({
        id: `bookmark-${bookmark.id}`,
        type: 'bookmark',
        title: bookmark.title,
        url: bookmark.url,
        description: bookmark.url,
        icon: <Star className="w-4 h-4 text-yellow-500" />,
      });
    });

    // Tab suggestions (switch to existing tab)
    const matchingTabs = tabs
      .filter(tab => 
        tab.title.toLowerCase().includes(lowerQuery) ||
        tab.url.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 2);

    matchingTabs.forEach(tab => {
      suggestions.push({
        id: `tab-${tab.id}`,
        type: 'history',
        title: `Switch to: ${tab.title}`,
        url: tab.url,
        description: tab.url,
        icon: <Clock className="w-4 h-4 text-purple-500" />,
      });
    });

    // AI suggestion (placeholder for future implementation)
    if (query.length > 3) {
      suggestions.push({
        id: 'ai-suggestion',
        type: 'ai',
        title: `Ask AI about "${query}"`,
        description: 'Get AI-powered insights',
        icon: <Zap className="w-4 h-4 text-orange-500" />,
      });
    }

    return suggestions.slice(0, 6); // Limit to 6 suggestions
  };

  useEffect(() => {
    const newSuggestions = generateSuggestions(query);
    setSuggestions(newSuggestions);
    setSelectedIndex(-1);
  }, [query, bookmarks, tabs]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setShowSuggestions(value.length > 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        handleSubmit();
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSubmit = () => {
    const selectedSuggestion = selectedIndex >= 0 ? suggestions[selectedIndex] : null;
    
    if (selectedSuggestion) {
      handleSuggestionClick(selectedSuggestion);
    } else if (query.trim()) {
      // Default behavior: navigate to URL or search
      const url = isValidUrl(query) 
        ? (query.startsWith('http') ? query : `https://${query}`)
        : `https://www.google.com/search?q=${encodeURIComponent(query)}`;
      
      if (activeTabId) {
        navigateTab(activeTabId, url);
      } else {
        createTab(url);
      }
    }
    
    setQuery('');
    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'history' && suggestion.id.startsWith('tab-')) {
      // Switch to existing tab
      const tabId = suggestion.id.replace('tab-', '');
      const { setActiveTab } = useBrowserStore.getState();
      setActiveTab(tabId);
    } else if (suggestion.url) {
      // Navigate to URL
      if (activeTabId) {
        navigateTab(activeTabId, suggestion.url);
      } else {
        createTab(suggestion.url);
      }
    } else if (suggestion.type === 'ai') {
      // TODO: Implement AI query handling
      console.log('AI query:', query);
    }
    
    setQuery('');
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const handleFocus = () => {
    if (query.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleBlur = () => {
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 150);
  };

  return (
    <div className="relative w-full max-w-2xl">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder="Search or enter URL..."
          className="
            w-full pl-10 pr-4 py-2 
            bg-white dark:bg-gray-800 
            border border-gray-300 dark:border-gray-600 
            rounded-full 
            text-sm text-gray-900 dark:text-gray-100
            placeholder-gray-500 dark:placeholder-gray-400
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
            transition-all duration-200
          "
        />
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.id}
              className={`
                flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors
                ${index === selectedIndex 
                  ? 'bg-blue-50 dark:bg-blue-900/20' 
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                }
                ${index === suggestions.length - 1 ? '' : 'border-b border-gray-100 dark:border-gray-700'}
              `}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className="flex-shrink-0">
                {suggestion.icon}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {suggestion.title}
                </div>
                {suggestion.description && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {suggestion.description}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
