import React, { useState, useRef, useEffect } from "react";

interface WebViewProps {
    url: string;
    onLoad?: () => void;
    onError?: () => void;
    onTitleChange?: (title: string) => void;
}

export function WebView({ url, onLoad, onError, onTitleChange }: WebViewProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const iframeRef = useRef<HTMLIFrameElement>(null);

    useEffect(() => {
        setIsLoading(true);
        setHasError(false);
    }, [url]);

    const handleLoad = () => {
        setIsLoading(false);
        onLoad?.();
        
        // Try to get the title from the iframe
        try {
            const iframe = iframeRef.current;
            if (iframe?.contentDocument) {
                const title = iframe.contentDocument.title;
                if (title) {
                    onTitleChange?.(title);
                }
            }
        } catch (e) {
            // Cross-origin restrictions prevent access to iframe content
            console.log("Cannot access iframe content due to CORS");
        }
    };

    const handleError = () => {
        setIsLoading(false);
        setHasError(true);
        onError?.();
    };

    // Check if URL is likely to be blocked
    const isLikelyBlocked = url.includes('google.com') || url.includes('facebook.com') || url.includes('youtube.com') || url.includes('twitter.com');

    if (hasError || isLikelyBlocked) {
        return (
            <div style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f8f9fa",
                flexDirection: "column",
                gap: "24px",
                padding: "40px"
            }}>
                <div style={{ fontSize: "64px" }}>🚫</div>
                <div style={{ textAlign: "center", maxWidth: "500px" }}>
                    <h2 style={{ color: "#dc3545", marginBottom: "16px", fontSize: "24px" }}>
                        Website Blocks Embedding
                    </h2>
                    <p style={{ color: "#6c757d", marginBottom: "24px", fontSize: "16px", lineHeight: "1.5" }}>
                        <strong>{new URL(url).hostname}</strong> prevents embedding for security reasons.
                        This is common with major sites like Google, Facebook, and YouTube.
                    </p>

                    <div style={{
                        backgroundColor: "#fff3cd",
                        border: "1px solid #ffeaa7",
                        borderRadius: "8px",
                        padding: "16px",
                        marginBottom: "24px",
                        textAlign: "left"
                    }}>
                        <h4 style={{ color: "#856404", margin: "0 0 8px 0", fontSize: "14px" }}>
                            💡 Why This Happens:
                        </h4>
                        <p style={{ color: "#856404", margin: 0, fontSize: "13px" }}>
                            Major websites use X-Frame-Options headers to prevent clickjacking attacks and maintain security.
                        </p>
                    </div>

                    <div style={{ display: "flex", gap: "12px", justifyContent: "center", flexWrap: "wrap" }}>
                        <button
                            onClick={() => window.open(url, '_blank')}
                            style={{
                                padding: "12px 24px",
                                backgroundColor: "#0d6efd",
                                color: "white",
                                border: "none",
                                borderRadius: "8px",
                                cursor: "pointer",
                                fontSize: "14px",
                                fontWeight: "500",
                                display: "flex",
                                alignItems: "center",
                                gap: "8px"
                            }}
                        >
                            🌐 Open in System Browser
                        </button>

                        <button
                            onClick={() => {
                                // Try alternative URLs that might work
                                const alternatives = [
                                    'https://example.com',
                                    'https://httpbin.org',
                                    'https://jsonplaceholder.typicode.com',
                                    'https://wikipedia.org'
                                ];
                                const randomAlt = alternatives[Math.floor(Math.random() * alternatives.length)];
                                window.location.href = `#${randomAlt}`;
                            }}
                            style={{
                                padding: "12px 24px",
                                backgroundColor: "#28a745",
                                color: "white",
                                border: "none",
                                borderRadius: "8px",
                                cursor: "pointer",
                                fontSize: "14px",
                                fontWeight: "500",
                                display: "flex",
                                alignItems: "center",
                                gap: "8px"
                            }}
                        >
                            🎲 Try Alternative Site
                        </button>
                    </div>

                    <div style={{ marginTop: "24px", padding: "16px", backgroundColor: "#e3f2fd", borderRadius: "8px" }}>
                        <p style={{ color: "#1565c0", margin: 0, fontSize: "13px", fontWeight: "500" }}>
                            🚀 <strong>Coming Soon:</strong> Advanced proxy and rendering engine to bypass these restrictions
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div style={{ width: "100%", height: "100%", position: "relative" }}>
            {isLoading && (
                <div style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#f8f9fa",
                    zIndex: 1000
                }}>
                    <div style={{ textAlign: "center" }}>
                        <div style={{ 
                            fontSize: "48px", 
                            marginBottom: "16px",
                            animation: "spin 2s linear infinite"
                        }}>
                            ⏳
                        </div>
                        <p style={{ color: "#6c757d", fontSize: "18px" }}>
                            Loading website...
                        </p>
                    </div>
                </div>
            )}
            
            <iframe
                ref={iframeRef}
                src={url}
                style={{
                    width: "100%",
                    height: "100%",
                    border: "none",
                    backgroundColor: "#ffffff"
                }}
                onLoad={handleLoad}
                onError={handleError}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-downloads"
                allow="accelerometer; autoplay; camera; encrypted-media; geolocation; gyroscope; microphone; midi; payment; usb; vr; xr-spatial-tracking"
                loading="lazy"
                title="Web content"
            />
        </div>
    );
}
