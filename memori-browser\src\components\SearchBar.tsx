﻿import React, { useState, useEffect } from "react";
import { <PERSON>, Mic, <PERSON>rkles, Clock, Bookmark, Brain, Zap, Globe, Star } from "lucide-react";

export function SearchBar() {
    const [query, setQuery] = useState("");
    const [suggestions, setSuggestions] = useState([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [isListening, setIsListening] = useState(false);
    const [aiThinking, setAiThinking] = useState(false);

    useEffect(() => {
        if (query.length > 2) {
            setAiThinking(true);
            setTimeout(() => {
                setSuggestions([
                    { icon: Brain, text: "AI suggests: React performance optimization techniques", type: "ai", confidence: 95 },
                    { icon: Clock, text: "You searched this 3 days ago: React hooks tutorial", type: "memory", confidence: 88 },
                    { icon: Star, text: "Trending: Next.js 14 new features", type: "trending", confidence: 92 },
                    { icon: Bookmark, text: "From your bookmarks: Advanced TypeScript patterns", type: "bookmark", confidence: 85 }
                ]);
                setShowSuggestions(true);
                setAiThinking(false);
            }, 800);
        } else {
            setSuggestions([]);
            setShowSuggestions(false);
        }
    }, [query]);

    const handleVoiceSearch = () => {
        setIsListening(!isListening);
        if (!isListening) {
            setQuery("Building an AI browser with autonomous agents");
            setTimeout(() => setIsListening(false), 3000);
        }
    };

    return (
        <div className="relative">
            <div className="p-4 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 border-b border-gray-700">
                <div className="relative max-w-4xl mx-auto">
                    <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                        <div className="relative bg-gray-900 border border-gray-600 rounded-lg">
                            <div className="flex items-center">
                                <Brain className="ml-4 w-5 h-5 text-purple-400" />
                                <input
                                    type="text"
                                    value={query}
                                    onChange={(e) => setQuery(e.target.value)}
                                    onFocus={() => query.length > 2 && setShowSuggestions(true)}
                                    className="flex-1 px-4 py-4 bg-transparent text-white placeholder-gray-400 focus:outline-none text-lg"
                                    placeholder="Ask AI anything, search the web, or enter URL..."
                                />
                                <div className="flex items-center gap-2 mr-4">
                                    {aiThinking && (
                                        <div className="flex items-center gap-2 text-purple-400">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400"></div>
                                            <span className="text-sm">AI thinking...</span>
                                        </div>
                                    )}
                                    <button
                                        onClick={handleVoiceSearch}
                                        className={`p-2 rounded-full transition-all duration-300 ${
                                            isListening 
                                                ? "bg-red-600 text-white animate-pulse" 
                                                : "text-gray-400 hover:text-white hover:bg-gray-700"
                                        }`}
                                    >
                                        <Mic className="w-5 h-5" />
                                    </button>
                                    <button className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                                        <Search className="w-5 h-5" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {showSuggestions && suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-50 bg-gray-800 border border-gray-600 rounded-b-lg shadow-2xl max-w-4xl mx-auto ml-4 mr-4">
                    {suggestions.map((suggestion, index) => (
                        <div
                            key={index}
                            className="flex items-center gap-4 px-6 py-4 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0 cursor-pointer group"
                        >
                            <suggestion.icon className="w-5 h-5 text-purple-400 group-hover:text-purple-300" />
                            <div className="flex-1">
                                <span className="text-white">{suggestion.text}</span>
                                <div className="flex items-center gap-2 mt-1">
                                    <span className={`text-xs px-2 py-1 rounded ${
                                        suggestion.type === "ai" ? "bg-purple-900 text-purple-300" :
                                        suggestion.type === "memory" ? "bg-blue-900 text-blue-300" :
                                        suggestion.type === "trending" ? "bg-orange-900 text-orange-300" :
                                        "bg-green-900 text-green-300"
                                    }`}>
                                        {suggestion.type.toUpperCase()}
                                    </span>
                                    <div className="flex items-center gap-1">
                                        <Zap className="w-3 h-3 text-yellow-400" />
                                        <span className="text-xs text-gray-400">{suggestion.confidence}% confidence</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
