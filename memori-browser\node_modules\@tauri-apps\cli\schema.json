{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Config", "description": "The Tauri configuration object. It is read from a file where you can define your frontend assets, configure the bundler, enable the app updater, define a system tray, enable APIs via the allowlist and more.\n\nThe configuration file is generated by the [`tauri init`](https://tauri.app/v1/api/cli#init) command that lives in your Tauri application source directory (src-tauri).\n\nOnce generated, you may modify it at will to customize your Tauri application.\n\n## File Formats\n\nBy default, the configuration is defined as a JSON file named `tauri.conf.json`.\n\nTauri also supports JSON5 and TOML files via the `config-json5` and `config-toml` Cargo features, respectively. The JSON5 file name must be either `tauri.conf.json` or `tauri.conf.json5`. The TOML file name is `Tauri.toml`.\n\n## Platform-Specific Configuration\n\nIn addition to the default configuration file, <PERSON><PERSON> can read a platform-specific configuration from `tauri.linux.conf.json`, `tauri.windows.conf.json`, and `tauri.macos.conf.json` (or `Tauri.linux.toml`, `Tauri.windows.toml` and `Tauri.macos.toml` if the `Tauri.toml` format is used), which gets merged with the main configuration object.\n\n## Configuration Structure\n\nThe configuration is composed of the following objects:\n\n- [`package`](#packageconfig): Package settings - [`tauri`](#tauriconfig): The Tauri config - [`build`](#buildconfig): The build configuration - [`plugins`](#pluginconfig): The plugins config\n\nExample tauri.config.json file:\n\n```json { \"build\": { \"beforeBuildCommand\": \"\", \"beforeDevCommand\": \"\", \"devPath\": \"../dist\", \"distDir\": \"../dist\" }, \"package\": { \"productName\": \"tauri-app\", \"version\": \"0.1.0\" }, \"tauri\": { \"allowlist\": { \"all\": true }, \"bundle\": {}, \"security\": { \"csp\": null }, \"updater\": { \"active\": false }, \"windows\": [ { \"fullscreen\": false, \"height\": 600, \"resizable\": true, \"title\": \"Tauri App\", \"width\": 800 } ] } } ```", "type": "object", "properties": {"$schema": {"description": "The JSON schema for the Tauri config.", "type": ["string", "null"]}, "package": {"description": "Package settings.", "default": {"productName": null, "version": null}, "allOf": [{"$ref": "#/definitions/PackageConfig"}]}, "tauri": {"description": "The Tauri configuration.", "default": {"allowlist": {"all": false, "app": {"all": false, "hide": false, "show": false}, "clipboard": {"all": false, "readText": false, "writeText": false}, "dialog": {"all": false, "ask": false, "confirm": false, "message": false, "open": false, "save": false}, "fs": {"all": false, "copyFile": false, "createDir": false, "exists": false, "readDir": false, "readFile": false, "removeDir": false, "removeFile": false, "renameFile": false, "scope": [], "writeFile": false}, "globalShortcut": {"all": false}, "http": {"all": false, "request": false, "scope": []}, "notification": {"all": false}, "os": {"all": false}, "path": {"all": false}, "process": {"all": false, "exit": false, "relaunch": false, "relaunchDangerousAllowSymlinkMacos": false}, "protocol": {"all": false, "asset": false, "assetScope": []}, "shell": {"all": false, "execute": false, "open": false, "scope": [], "sidecar": false}, "window": {"all": false, "center": false, "close": false, "create": false, "hide": false, "maximize": false, "minimize": false, "print": false, "requestUserAttention": false, "setAlwaysOnTop": false, "setClosable": false, "setContentProtected": false, "setCursorGrab": false, "setCursorIcon": false, "setCursorPosition": false, "setCursorVisible": false, "setDecorations": false, "setFocus": false, "setFullscreen": false, "setIcon": false, "setIgnoreCursorEvents": false, "setMaxSize": false, "setMaximizable": false, "setMinSize": false, "setMinimizable": false, "setPosition": false, "setResizable": false, "setSize": false, "setSkipTaskbar": false, "setTitle": false, "show": false, "startDragging": false, "unmaximize": false, "unminimize": false}}, "bundle": {"active": false, "appimage": {"bundleMediaFramework": false}, "deb": {"files": {}}, "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "icon": [], "identifier": "", "macOS": {"hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "rpm": {"epoch": 0, "files": {}, "release": "1"}, "targets": "all", "useLocalToolsDir": false, "windows": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "signCommand": null, "timestampUrl": null, "tsp": false, "webviewFixedRuntimePath": null, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}}, "macOSPrivateApi": false, "pattern": {"use": "brownfield"}, "security": {"dangerousDisableAssetCspModification": false, "dangerousRemoteDomainIpcAccess": [], "dangerousUseHttpScheme": false, "freezePrototype": false}, "updater": {"active": false, "dialog": true, "pubkey": "", "windows": {"installMode": "passive", "installerArgs": []}}, "windows": []}, "allOf": [{"$ref": "#/definitions/TauriConfig"}]}, "build": {"description": "The build configuration.", "default": {"devPath": "http://localhost:8080/", "distDir": "../dist", "withGlobalTauri": false}, "allOf": [{"$ref": "#/definitions/BuildConfig"}]}, "plugins": {"description": "The plugins config.", "default": {}, "allOf": [{"$ref": "#/definitions/PluginConfig"}]}}, "additionalProperties": false, "definitions": {"PackageConfig": {"description": "The package configuration.\n\nSee more: https://tauri.app/v1/api/config#packageconfig", "type": "object", "properties": {"productName": {"description": "App name.", "type": ["string", "null"], "pattern": "^[^/\\:*?\"<>|]+$"}, "version": {"description": "App version. It is a semver version number or a path to a `package.json` file containing the `version` field. If removed the version number from `Cargo.toml` is used.", "type": ["string", "null"]}}, "additionalProperties": false}, "TauriConfig": {"description": "The Tauri configuration object.\n\nSee more: https://tauri.app/v1/api/config#tauriconfig", "type": "object", "properties": {"pattern": {"description": "The pattern to use.", "default": {"use": "brownfield"}, "allOf": [{"$ref": "#/definitions/PatternKind"}]}, "windows": {"description": "The windows configuration.", "default": [], "type": "array", "items": {"$ref": "#/definitions/WindowConfig"}}, "cli": {"description": "The CLI configuration.", "anyOf": [{"$ref": "#/definitions/CliConfig"}, {"type": "null"}]}, "bundle": {"description": "The bundler configuration.", "default": {"active": false, "appimage": {"bundleMediaFramework": false}, "deb": {"files": {}}, "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "icon": [], "identifier": "", "macOS": {"hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "rpm": {"epoch": 0, "files": {}, "release": "1"}, "targets": "all", "useLocalToolsDir": false, "windows": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "signCommand": null, "timestampUrl": null, "tsp": false, "webviewFixedRuntimePath": null, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}}, "allOf": [{"$ref": "#/definitions/BundleConfig"}]}, "allowlist": {"description": "The allowlist configuration.", "default": {"all": false, "app": {"all": false, "hide": false, "show": false}, "clipboard": {"all": false, "readText": false, "writeText": false}, "dialog": {"all": false, "ask": false, "confirm": false, "message": false, "open": false, "save": false}, "fs": {"all": false, "copyFile": false, "createDir": false, "exists": false, "readDir": false, "readFile": false, "removeDir": false, "removeFile": false, "renameFile": false, "scope": [], "writeFile": false}, "globalShortcut": {"all": false}, "http": {"all": false, "request": false, "scope": []}, "notification": {"all": false}, "os": {"all": false}, "path": {"all": false}, "process": {"all": false, "exit": false, "relaunch": false, "relaunchDangerousAllowSymlinkMacos": false}, "protocol": {"all": false, "asset": false, "assetScope": []}, "shell": {"all": false, "execute": false, "open": false, "scope": [], "sidecar": false}, "window": {"all": false, "center": false, "close": false, "create": false, "hide": false, "maximize": false, "minimize": false, "print": false, "requestUserAttention": false, "setAlwaysOnTop": false, "setClosable": false, "setContentProtected": false, "setCursorGrab": false, "setCursorIcon": false, "setCursorPosition": false, "setCursorVisible": false, "setDecorations": false, "setFocus": false, "setFullscreen": false, "setIcon": false, "setIgnoreCursorEvents": false, "setMaxSize": false, "setMaximizable": false, "setMinSize": false, "setMinimizable": false, "setPosition": false, "setResizable": false, "setSize": false, "setSkipTaskbar": false, "setTitle": false, "show": false, "startDragging": false, "unmaximize": false, "unminimize": false}}, "allOf": [{"$ref": "#/definitions/AllowlistConfig"}]}, "security": {"description": "Security configuration.", "default": {"dangerousDisableAssetCspModification": false, "dangerousRemoteDomainIpcAccess": [], "dangerousUseHttpScheme": false, "freezePrototype": false}, "allOf": [{"$ref": "#/definitions/SecurityConfig"}]}, "updater": {"description": "The updater configuration.", "default": {"active": false, "dialog": true, "pubkey": "", "windows": {"installMode": "passive", "installerArgs": []}}, "allOf": [{"$ref": "#/definitions/UpdaterConfig"}]}, "systemTray": {"description": "Configuration for app system tray.", "anyOf": [{"$ref": "#/definitions/SystemTrayConfig"}, {"type": "null"}]}, "macOSPrivateApi": {"description": "MacOS private API configuration. Enables the transparent background API and sets the `fullScreenEnabled` preference to `true`.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "PatternKind": {"description": "The application pattern.", "oneOf": [{"description": "Brownfield pattern.", "type": "object", "required": ["use"], "properties": {"use": {"type": "string", "enum": ["brownfield"]}}}, {"description": "Isolation pattern. Recommended for security purposes.", "type": "object", "required": ["options", "use"], "properties": {"use": {"type": "string", "enum": ["isolation"]}, "options": {"type": "object", "required": ["dir"], "properties": {"dir": {"description": "The dir containing the index.html file that contains the secure isolation application.", "type": "string"}}}}}]}, "WindowConfig": {"description": "The window configuration object.\n\nSee more: https://tauri.app/v1/api/config#windowconfig", "type": "object", "properties": {"label": {"description": "The window identifier. It must be alphanumeric.", "default": "main", "type": "string"}, "url": {"description": "The window webview URL.", "default": "index.html", "allOf": [{"$ref": "#/definitions/WindowUrl"}]}, "userAgent": {"description": "The user agent for the webview", "type": ["string", "null"]}, "fileDropEnabled": {"description": "Whether the file drop is enabled or not on the webview. By default it is enabled.\n\nDisabling it is required to use drag and drop on the frontend on Windows.", "default": true, "type": "boolean"}, "center": {"description": "Whether or not the window starts centered or not.", "default": false, "type": "boolean"}, "x": {"description": "The horizontal position of the window's top left corner", "type": ["number", "null"], "format": "double"}, "y": {"description": "The vertical position of the window's top left corner", "type": ["number", "null"], "format": "double"}, "width": {"description": "The window width.", "default": 800.0, "type": "number", "format": "double"}, "height": {"description": "The window height.", "default": 600.0, "type": "number", "format": "double"}, "minWidth": {"description": "The min window width.", "type": ["number", "null"], "format": "double"}, "minHeight": {"description": "The min window height.", "type": ["number", "null"], "format": "double"}, "maxWidth": {"description": "The max window width.", "type": ["number", "null"], "format": "double"}, "maxHeight": {"description": "The max window height.", "type": ["number", "null"], "format": "double"}, "resizable": {"description": "Whether the window is resizable or not. When resizable is set to false, native window's maximize button is automatically disabled.", "default": true, "type": "boolean"}, "maximizable": {"description": "Whether the window's native maximize button is enabled or not. If resizable is set to false, this setting is ignored.\n\n## Platform-specific\n\n- **macOS:** Disables the \"zoom\" button in the window titlebar, which is also used to enter fullscreen mode. - **Linux / iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "minimizable": {"description": "Whether the window's native minimize button is enabled or not.\n\n## Platform-specific\n\n- **Linux / iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "closable": {"description": "Whether the window's native close button is enabled or not.\n\n## Platform-specific\n\n- **Linux:** \"GTK+ will do its best to convince the window manager not to show a close button. Depending on the system, this function may not have any effect when called on a window that is already visible\" - **iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "title": {"description": "The window title.", "default": "<PERSON><PERSON>", "type": "string"}, "fullscreen": {"description": "Whether the window starts as fullscreen or not.", "default": false, "type": "boolean"}, "focus": {"description": "Whether the window will be initially focused or not.", "default": true, "type": "boolean"}, "transparent": {"description": "Whether the window is transparent or not.\n\nNote that on `macOS` this requires the `macos-private-api` feature flag, enabled under `tauri > macOSPrivateApi`. WARNING: Using private APIs on `macOS` prevents your application from being accepted to the `App Store`.", "default": false, "type": "boolean"}, "maximized": {"description": "Whether the window is maximized or not.", "default": false, "type": "boolean"}, "visible": {"description": "Whether the window is visible or not.", "default": true, "type": "boolean"}, "decorations": {"description": "Whether the window should have borders and bars.", "default": true, "type": "boolean"}, "alwaysOnTop": {"description": "Whether the window should always be on top of other windows.", "default": false, "type": "boolean"}, "contentProtected": {"description": "Prevents the window contents from being captured by other apps.", "default": false, "type": "boolean"}, "skipTaskbar": {"description": "If `true`, hides the window icon from the taskbar on Windows and Linux.", "default": false, "type": "boolean"}, "theme": {"description": "The initial window theme. Defaults to the system theme. Only implemented on Windows and macOS 10.14+.", "anyOf": [{"$ref": "#/definitions/Theme"}, {"type": "null"}]}, "titleBarStyle": {"description": "The style of the macOS title bar.", "default": "Visible", "allOf": [{"$ref": "#/definitions/TitleBarStyle"}]}, "hiddenTitle": {"description": "If `true`, sets the window title to be hidden on macOS.", "default": false, "type": "boolean"}, "acceptFirstMouse": {"description": "Whether clicking an inactive window also clicks through to the webview on macOS.", "default": false, "type": "boolean"}, "tabbingIdentifier": {"description": "Defines the window [tabbing identifier] for macOS.\n\nWindows with matching tabbing identifiers will be grouped together. If the tabbing identifier is not set, automatic tabbing will be disabled.\n\n[tabbing identifier]: <https://developer.apple.com/documentation/appkit/nswindow/1644704-tabbingidentifier>", "type": ["string", "null"]}, "additionalBrowserArgs": {"description": "Defines additional browser arguments on Windows. By default wry passes `--disable-features=msWebOOUI,msPdfOOUI,msSmartScreenProtection` so if you use this method, you also need to disable these components by yourself if you want.", "type": ["string", "null"]}}, "additionalProperties": false}, "WindowUrl": {"description": "An URL to open on a Tauri webview window.", "anyOf": [{"description": "An external URL.", "type": "string", "format": "uri"}, {"description": "The path portion of an app URL. For instance, to load `tauri://localhost/users/john`, you can simply provide `users/john` in this configuration.", "type": "string"}]}, "Theme": {"description": "System theme.", "oneOf": [{"description": "Light theme.", "type": "string", "enum": ["Light"]}, {"description": "Dark theme.", "type": "string", "enum": ["Dark"]}]}, "TitleBarStyle": {"description": "How the window title bar should be displayed on macOS.", "oneOf": [{"description": "A normal title bar.", "type": "string", "enum": ["Visible"]}, {"description": "Makes the title bar transparent, so the window background color is shown instead.\n\nUseful if you don't need to have actual HTML under the title bar. This lets you avoid the caveats of using `TitleBarStyle::Overlay`. Will be more useful when <PERSON><PERSON> lets you set a custom window background color.", "type": "string", "enum": ["Transparent"]}, {"description": "Shows the title bar as a transparent overlay over the window's content.\n\nKeep in mind: - The height of the title bar is different on different OS versions, which can lead to the window controls and title not being where you expect them to be. - You need to define a custom drag region to make your window draggable, however due to a limitation you can't drag the window when it's not in focus <https://github.com/tauri-apps/tauri/issues/4316>. - The color of the window title depends on the system theme.", "type": "string", "enum": ["Overlay"]}]}, "CliConfig": {"description": "describes a CLI configuration\n\nSee more: https://tauri.app/v1/api/config#cliconfig", "type": "object", "properties": {"description": {"description": "Command description which will be shown on the help information.", "type": ["string", "null"]}, "longDescription": {"description": "Command long description which will be shown on the help information.", "type": ["string", "null"]}, "beforeHelp": {"description": "Adds additional help information to be displayed in addition to auto-generated help. This information is displayed before the auto-generated help information. This is often used for header information.", "type": ["string", "null"]}, "afterHelp": {"description": "Adds additional help information to be displayed in addition to auto-generated help. This information is displayed after the auto-generated help information. This is often used to describe how to use the arguments, or caveats to be noted.", "type": ["string", "null"]}, "args": {"description": "List of arguments for the command", "type": ["array", "null"], "items": {"$ref": "#/definitions/CliArg"}}, "subcommands": {"description": "List of subcommands of this command", "type": ["object", "null"], "additionalProperties": {"$ref": "#/definitions/CliConfig"}}}, "additionalProperties": false}, "CliArg": {"description": "A CLI argument definition.", "type": "object", "required": ["name"], "properties": {"short": {"description": "The short version of the argument, without the preceding -.\n\nNOTE: Any leading `-` characters will be stripped, and only the first non-character will be used as the short version.", "type": ["string", "null"], "maxLength": 1, "minLength": 1}, "name": {"description": "The unique argument name", "type": "string"}, "description": {"description": "The argument description which will be shown on the help information. Typically, this is a short (one line) description of the arg.", "type": ["string", "null"]}, "longDescription": {"description": "The argument long description which will be shown on the help information. Typically this a more detailed (multi-line) message that describes the argument.", "type": ["string", "null"]}, "takesValue": {"description": "Specifies that the argument takes a value at run time.\n\nNOTE: values for arguments may be specified in any of the following methods - Using a space such as -o value or --option value - Using an equals and no space such as -o=value or --option=value - Use a short and no space such as -ovalue", "default": false, "type": "boolean"}, "multiple": {"description": "Specifies that the argument may have an unknown number of multiple values. Without any other settings, this argument may appear only once.\n\nFor example, --opt val1 val2 is allowed, but --opt val1 val2 --opt val3 is not.\n\nNOTE: Setting this requires `takes_value` to be set to true.", "default": false, "type": "boolean"}, "multipleOccurrences": {"description": "Specifies that the argument may appear more than once. For flags, this results in the number of occurrences of the flag being recorded. For example -ddd or -d -d -d would count as three occurrences. For options or arguments that take a value, this does not affect how many values they can accept. (i.e. only one at a time is allowed)\n\nFor example, --opt val1 --opt val2 is allowed, but --opt val1 val2 is not.", "default": false, "type": "boolean"}, "numberOfValues": {"description": "Specifies how many values are required to satisfy this argument. For example, if you had a `-f <file>` argument where you wanted exactly 3 'files' you would set `number_of_values = 3`, and this argument wouldn't be satisfied unless the user provided 3 and only 3 values.\n\n**NOTE:** Does *not* require `multiple_occurrences = true` to be set. Setting `multiple_occurrences = true` would allow `-f <file> <file> <file> -f <file> <file> <file>` where as *not* setting it would only allow one occurrence of this argument.\n\n**NOTE:** implicitly sets `takes_value = true` and `multiple_values = true`.", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "possibleValues": {"description": "Specifies a list of possible values for this argument. At runtime, the CLI verifies that only one of the specified values was used, or fails with an error message.", "type": ["array", "null"], "items": {"type": "string"}}, "minValues": {"description": "Specifies the minimum number of values for this argument. For example, if you had a -f `<file>` argument where you wanted at least 2 'files', you would set `minValues: 2`, and this argument would be satisfied if the user provided, 2 or more values.", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "maxValues": {"description": "Specifies the maximum number of values are for this argument. For example, if you had a -f `<file>` argument where you wanted up to 3 'files', you would set .max_values(3), and this argument would be satisfied if the user provided, 1, 2, or 3 values.", "type": ["integer", "null"], "format": "uint", "minimum": 0.0}, "required": {"description": "Sets whether or not the argument is required by default.\n\n- Required by default means it is required, when no other conflicting rules have been evaluated - Conflicting rules take precedence over being required.", "default": false, "type": "boolean"}, "requiredUnlessPresent": {"description": "Sets an arg that override this arg's required setting i.e. this arg will be required unless this other argument is present.", "type": ["string", "null"]}, "requiredUnlessPresentAll": {"description": "Sets args that override this arg's required setting i.e. this arg will be required unless all these other arguments are present.", "type": ["array", "null"], "items": {"type": "string"}}, "requiredUnlessPresentAny": {"description": "Sets args that override this arg's required setting i.e. this arg will be required unless at least one of these other arguments are present.", "type": ["array", "null"], "items": {"type": "string"}}, "conflictsWith": {"description": "Sets a conflicting argument by name i.e. when using this argument, the following argument can't be present and vice versa.", "type": ["string", "null"]}, "conflictsWithAll": {"description": "The same as conflictsWith but allows specifying multiple two-way conflicts per argument.", "type": ["array", "null"], "items": {"type": "string"}}, "requires": {"description": "Tets an argument by name that is required when this one is present i.e. when using this argument, the following argument must be present.", "type": ["string", "null"]}, "requiresAll": {"description": "Sts multiple arguments by names that are required when this one is present i.e. when using this argument, the following arguments must be present.", "type": ["array", "null"], "items": {"type": "string"}}, "requiresIf": {"description": "Allows a conditional requirement with the signature [arg, value] the requirement will only become valid if `arg`'s value equals `${value}`.", "type": ["array", "null"], "items": {"type": "string"}}, "requiredIfEq": {"description": "Allows specifying that an argument is required conditionally with the signature [arg, value] the requirement will only become valid if the `arg`'s value equals `${value}`.", "type": ["array", "null"], "items": {"type": "string"}}, "requireEquals": {"description": "Requires that options use the --option=val syntax i.e. an equals between the option and associated value.", "type": ["boolean", "null"]}, "index": {"description": "The positional argument index, starting at 1.\n\nThe index refers to position according to other positional argument. It does not define position in the argument list as a whole. When utilized with multiple=true, only the last positional argument may be defined as multiple (i.e. the one with the highest index).", "type": ["integer", "null"], "format": "uint", "minimum": 1.0}}, "additionalProperties": false}, "BundleConfig": {"description": "Configuration for tauri-bundler.\n\nSee more: https://tauri.app/v1/api/config#bundleconfig", "type": "object", "required": ["identifier"], "properties": {"active": {"description": "Whether <PERSON><PERSON> should bundle your application or just output the executable.", "default": false, "type": "boolean"}, "targets": {"description": "The bundle targets, currently supports [\"deb\", \"rpm\", \"appimage\", \"nsis\", \"msi\", \"app\", \"dmg\", \"updater\"] or \"all\".", "default": "all", "allOf": [{"$ref": "#/definitions/BundleTarget"}]}, "identifier": {"description": "The application identifier in reverse domain name notation (e.g. `com.tauri.example`). This string must be unique across applications since it is used in system configurations like the bundle ID and path to the webview data directory. This string must contain only alphanumeric characters (A-Z, a-z, and 0-9), hyphens (-), and periods (.).", "type": "string"}, "publisher": {"description": "The application's publisher. Defaults to the second element in the identifier string. Currently maps to the Manufacturer property of the Windows Installer.", "type": ["string", "null"]}, "icon": {"description": "The app's icons", "default": [], "type": "array", "items": {"type": "string"}}, "resources": {"description": "App resources to bundle. Each resource is a path to a file or directory. Glob patterns are supported.", "anyOf": [{"$ref": "#/definitions/BundleResources"}, {"type": "null"}]}, "copyright": {"description": "A copyright string associated with your application.", "type": ["string", "null"]}, "category": {"description": "The application kind.\n\nShould be one of the following: Business, DeveloperTool, Education, Entertainment, Finance, Game, ActionGame, AdventureGame, ArcadeGame, BoardGame, CardGame, CasinoGame, DiceGame, EducationalGame, FamilyGame, KidsGame, MusicGame, PuzzleGame, RacingGame, RolePlayingGame, SimulationGame, SportsGame, StrategyGame, TriviaGame, WordGame, GraphicsAndDesign, HealthcareAndFitness, Lifestyle, Medical, Music, News, Photography, Productivity, Reference, SocialNetworking, Sports, Travel, Utility, Video, Weather.", "type": ["string", "null"]}, "shortDescription": {"description": "A short description of your application.", "type": ["string", "null"]}, "longDescription": {"description": "A longer, multi-line description of the application.", "type": ["string", "null"]}, "useLocalToolsDir": {"description": "Whether to use the project's `target` directory, for caching build tools (e.g., Wix and NSIS) when building this application. Defaults to `false`.\n\nIf true, tools will be cached in `target\\.tauri-tools`. If false, tools will be cached in the current user's platform-specific cache directory.\n\nAn example where it can be appropriate to set this to `true` is when building this application as a Windows System user (e.g., AWS EC2 workloads), because the Window system's app data directory is restricted.", "default": false, "type": "boolean"}, "appimage": {"description": "Configuration for the AppImage bundle.", "default": {"bundleMediaFramework": false}, "allOf": [{"$ref": "#/definitions/AppImageConfig"}]}, "deb": {"description": "Configuration for the Debian bundle.", "default": {"files": {}}, "allOf": [{"$ref": "#/definitions/DebConfig"}]}, "rpm": {"description": "Configuration for the RPM bundle.", "default": {"epoch": 0, "files": {}, "release": "1"}, "allOf": [{"$ref": "#/definitions/RpmConfig"}]}, "dmg": {"description": "DMG-specific settings.", "default": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "allOf": [{"$ref": "#/definitions/DmgConfig"}]}, "macOS": {"description": "Configuration for the macOS bundles.", "default": {"hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "allOf": [{"$ref": "#/definitions/MacConfig"}]}, "externalBin": {"description": "A list of—either absolute or relative—paths to binaries to embed with your application.\n\nNote that <PERSON><PERSON> will look for system-specific binaries following the pattern \"binary-name{-target-triple}{.system-extension}\".\n\nE.g. for the external binary \"my-binary\", <PERSON><PERSON> looks for:\n\n- \"my-binary-x86_64-pc-windows-msvc.exe\" for Windows - \"my-binary-x86_64-apple-darwin\" for macOS - \"my-binary-x86_64-unknown-linux-gnu\" for Linux\n\nso don't forget to provide binaries for all targeted platforms.", "type": ["array", "null"], "items": {"type": "string"}}, "windows": {"description": "Configuration for the Windows bundle.", "default": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "signCommand": null, "timestampUrl": null, "tsp": false, "webviewFixedRuntimePath": null, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}, "allOf": [{"$ref": "#/definitions/WindowsConfig"}]}}, "additionalProperties": false}, "BundleTarget": {"description": "Targets to bundle. Each value is case insensitive.", "anyOf": [{"description": "Bundle all targets.", "enum": ["all"]}, {"description": "A list of bundle targets.", "type": "array", "items": {"$ref": "#/definitions/BundleType"}}, {"description": "A single bundle target.", "allOf": [{"$ref": "#/definitions/BundleType"}]}]}, "BundleType": {"description": "A bundle referenced by tauri-bundler.", "oneOf": [{"description": "The debian bundle (.deb).", "type": "string", "enum": ["deb"]}, {"description": "The RPM bundle (.rpm).", "type": "string", "enum": ["rpm"]}, {"description": "The AppImage bundle (.appimage).", "type": "string", "enum": ["appimage"]}, {"description": "The Microsoft Installer bundle (.msi).", "type": "string", "enum": ["msi"]}, {"description": "The NSIS bundle (.exe).", "type": "string", "enum": ["nsis"]}, {"description": "The macOS application bundle (.app).", "type": "string", "enum": ["app"]}, {"description": "The Apple Disk Image bundle (.dmg).", "type": "string", "enum": ["dmg"]}, {"description": "The Tauri updater bundle.", "type": "string", "enum": ["updater"]}]}, "BundleResources": {"description": "Definition for bundle resources. Can be either a list of paths to include or a map of source to target paths.", "anyOf": [{"description": "A list of paths to include.", "type": "array", "items": {"type": "string"}}, {"description": "A map of source to target paths.", "type": "object", "additionalProperties": {"type": "string"}}]}, "AppImageConfig": {"description": "Configuration for AppImage bundles.\n\nSee more: https://tauri.app/v1/api/config#appimageconfig", "type": "object", "properties": {"bundleMediaFramework": {"description": "Include additional gstreamer dependencies needed for audio and video playback. This increases the bundle size by ~15-35MB depending on your build system.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "DebConfig": {"description": "Configuration for Debian (.deb) bundles.\n\nSee more: https://tauri.app/v1/api/config#debconfig", "type": "object", "properties": {"depends": {"description": "The list of deb dependencies your application relies on.", "type": ["array", "null"], "items": {"type": "string"}}, "provides": {"description": "The list of dependencies the package provides.", "type": ["array", "null"], "items": {"type": "string"}}, "conflicts": {"description": "The list of package conflicts.", "type": ["array", "null"], "items": {"type": "string"}}, "replaces": {"description": "The list of package replaces.", "type": ["array", "null"], "items": {"type": "string"}}, "files": {"description": "The files to include on the package.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}, "desktopTemplate": {"description": "Path to a custom desktop file Handlebars template.\n\nAvailable variables: `categories`, `comment` (optional), `exec`, `icon` and `name`.", "type": ["string", "null"]}, "section": {"description": "Define the section in Debian Control file. See : https://www.debian.org/doc/debian-policy/ch-archive.html#s-subsections", "type": ["string", "null"]}, "priority": {"description": "Change the priority of the Debian Package. By default, it is set to `optional`. Recognized Priorities as of now are :  `required`, `important`, `standard`, `optional`, `extra`", "type": ["string", "null"]}, "changelog": {"description": "Path of the uncompressed Changelog file, to be stored at /usr/share/doc/package-name/changelog.gz. See https://www.debian.org/doc/debian-policy/ch-docs.html#changelog-files-and-release-notes", "type": ["string", "null"]}}, "additionalProperties": false}, "RpmConfig": {"description": "Configuration for RPM bundles.", "type": "object", "properties": {"license": {"description": "The package's license identifier. If not set, defaults to the license from the Cargo.toml file.", "type": ["string", "null"]}, "depends": {"description": "The list of RPM dependencies your application relies on.", "type": ["array", "null"], "items": {"type": "string"}}, "provides": {"description": "The list of RPM dependencies your application provides.", "type": ["array", "null"], "items": {"type": "string"}}, "conflicts": {"description": "The list of RPM dependencies your application conflicts with. They must not be present in order for the package to be installed.", "type": ["array", "null"], "items": {"type": "string"}}, "obsoletes": {"description": "The list of RPM dependencies your application supersedes - if this package is installed, packages listed as \"obsoletes\" will be automatically removed (if they are present).", "type": ["array", "null"], "items": {"type": "string"}}, "release": {"description": "The RPM release tag.", "default": "1", "type": "string"}, "epoch": {"description": "The RPM epoch.", "default": 0, "type": "integer", "format": "uint32", "minimum": 0.0}, "files": {"description": "The files to include on the package.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}, "desktopTemplate": {"description": "Path to a custom desktop file Handlebars template.\n\nAvailable variables: `categories`, `comment` (optional), `exec`, `icon` and `name`.", "type": ["string", "null"]}}, "additionalProperties": false}, "DmgConfig": {"description": "Configuration for Apple Disk Image (.dmg) bundles.\n\nSee more: https://tauri.app/v1/api/config#dmgconfig", "type": "object", "properties": {"background": {"description": "Image to use as the background in dmg file. Accepted formats: `png`/`jpg`/`gif`.", "type": ["string", "null"]}, "windowPosition": {"description": "Position of volume window on screen.", "anyOf": [{"$ref": "#/definitions/Position"}, {"type": "null"}]}, "windowSize": {"description": "Size of volume window.", "default": {"height": 400, "width": 660}, "allOf": [{"$ref": "#/definitions/Size"}]}, "appPosition": {"description": "Position of app file on window.", "default": {"x": 180, "y": 170}, "allOf": [{"$ref": "#/definitions/Position"}]}, "applicationFolderPosition": {"description": "Position of application folder on window.", "default": {"x": 480, "y": 170}, "allOf": [{"$ref": "#/definitions/Position"}]}}, "additionalProperties": false}, "Position": {"description": "Position coordinates struct.", "type": "object", "required": ["x", "y"], "properties": {"x": {"description": "X coordinate.", "type": "integer", "format": "uint32", "minimum": 0.0}, "y": {"description": "Y coordinate.", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, "Size": {"description": "Size of the window.", "type": "object", "required": ["height", "width"], "properties": {"width": {"description": "Width of the window.", "type": "integer", "format": "uint32", "minimum": 0.0}, "height": {"description": "Height of the window.", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, "MacConfig": {"description": "Configuration for the macOS bundles.\n\nSee more: https://tauri.app/v1/api/config#macconfig", "type": "object", "properties": {"frameworks": {"description": "A list of strings indicating any macOS X frameworks that need to be bundled with the application.\n\nIf a name is used, \".framework\" must be omitted and it will look for standard install locations. You may also use a path to a specific framework.", "type": ["array", "null"], "items": {"type": "string"}}, "minimumSystemVersion": {"description": "A version string indicating the minimum macOS X version that the bundled application supports. Defaults to `10.13`.\n\nSetting it to `null` completely removes the `LSMinimumSystemVersion` field on the bundle's `Info.plist` and the `MACOSX_DEPLOYMENT_TARGET` environment variable.\n\nAn empty string is considered an invalid value so the default value is used.", "default": "10.13", "type": ["string", "null"]}, "exceptionDomain": {"description": "Allows your application to communicate with the outside world. It should be a lowercase, without port and protocol domain name.", "type": ["string", "null"]}, "license": {"description": "The path to the license file to add to the DMG bundle.", "type": ["string", "null"]}, "signingIdentity": {"description": "Identity to use for code signing.", "type": ["string", "null"]}, "hardenedRuntime": {"description": "Whether the codesign should enable [hardened runtime] (for executables) or not.\n\n[hardened runtime]: <https://developer.apple.com/documentation/security/hardened_runtime>", "default": true, "type": "boolean"}, "providerShortName": {"description": "Provider short name for notarization.", "type": ["string", "null"]}, "entitlements": {"description": "Path to the entitlements file.", "type": ["string", "null"]}}, "additionalProperties": false}, "WindowsConfig": {"description": "Windows bundler configuration.\n\nSee more: https://tauri.app/v1/api/config#windowsconfig", "type": "object", "properties": {"digestAlgorithm": {"description": "Specifies the file digest algorithm to use for creating file signatures. Required for code signing. SHA-256 is recommended.", "type": ["string", "null"]}, "certificateThumbprint": {"description": "Specifies the SHA1 hash of the signing certificate.", "type": ["string", "null"]}, "timestampUrl": {"description": "Server to use during timestamping.", "type": ["string", "null"]}, "tsp": {"description": "Whether to use Time-Stamp Protocol (TSP, a.k.a. RFC 3161) for the timestamp server. Your code signing provider may use a TSP timestamp server, like e.g. SSL.com does. If so, enable TSP by setting to true.", "default": false, "type": "boolean"}, "webviewInstallMode": {"description": "The installation mode for the Webview2 runtime.", "default": {"silent": true, "type": "downloadBootstrapper"}, "allOf": [{"$ref": "#/definitions/WebviewInstallMode"}]}, "webviewFixedRuntimePath": {"description": "Path to the webview fixed runtime to use. Overwrites [`Self::webview_install_mode`] if set.\n\nWill be removed in v2, prefer the [`Self::webview_install_mode`] option.\n\nThe fixed version can be downloaded [on the official website](https://developer.microsoft.com/en-us/microsoft-edge/webview2/#download-section). The `.cab` file must be extracted to a folder and this folder path must be defined on this field.", "type": ["string", "null"]}, "allowDowngrades": {"description": "Validates a second app installation, blocking the user from installing an older version if set to `false`.\n\nFor instance, if `1.2.1` is installed, the user won't be able to install app version `1.2.0` or `1.1.5`.\n\nThe default value of this flag is `true`.", "default": true, "type": "boolean"}, "wix": {"description": "Configuration for the MSI generated with WiX.", "anyOf": [{"$ref": "#/definitions/WixConfig"}, {"type": "null"}]}, "nsis": {"description": "Configuration for the installer generated with NSIS.", "anyOf": [{"$ref": "#/definitions/NsisConfig"}, {"type": "null"}]}, "signCommand": {"description": "Specify a custom command to sign the binaries. This command needs to have a `%1` in it which is just a placeholder for the binary path, which we will detect and replace before calling the command.\n\nExample: ```text sign-cli --arg1 --arg2 %1 ```\n\nBy Default we use `signtool.exe` which can be found only on Windows so if you are on another platform and want to cross-compile and sign you will need to use another tool like `osslsigncode`.", "type": ["string", "null"]}}, "additionalProperties": false}, "WebviewInstallMode": {"description": "Install modes for the Webview2 runtime. Note that for the updater bundle [`Self::DownloadBootstrapper`] is used.\n\nFor more information see <https://tauri.app/v1/guides/building/windows>.", "oneOf": [{"description": "Do not install the Webview2 as part of the Windows Installer.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["skip"]}}, "additionalProperties": false}, {"description": "Download the bootstrapper and run it. Requires an internet connection. Results in a smaller installer size, but is not recommended on Windows 7.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["downloadBootstrapper"]}, "silent": {"description": "Instructs the installer to run the bootstrapper in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed the bootstrapper and run it. Requires an internet connection. Increases the installer size by around 1.8MB, but offers better support on Windows 7.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["embed<PERSON><PERSON><PERSON><PERSON>"]}, "silent": {"description": "Instructs the installer to run the bootstrapper in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed the offline installer and run it. Does not require an internet connection. Increases the installer size by around 127MB.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["offlineInstaller"]}, "silent": {"description": "Instructs the installer to run the installer in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed a fixed webview2 version and use it at runtime. Increases the installer size by around 180MB.", "type": "object", "required": ["path", "type"], "properties": {"type": {"type": "string", "enum": ["fixedRuntime"]}, "path": {"description": "The path to the fixed runtime to use.\n\nThe fixed version can be downloaded [on the official website](https://developer.microsoft.com/en-us/microsoft-edge/webview2/#download-section). The `.cab` file must be extracted to a folder and this folder path must be defined on this field.", "type": "string"}}, "additionalProperties": false}]}, "WixConfig": {"description": "Configuration for the MSI bundle using WiX.\n\nSee more: https://tauri.app/v1/api/config#wixconfig", "type": "object", "properties": {"language": {"description": "The installer languages to build. See <https://docs.microsoft.com/en-us/windows/win32/msi/localizing-the-error-and-actiontext-tables>.", "default": "en-US", "allOf": [{"$ref": "#/definitions/WixLanguage"}]}, "template": {"description": "A custom .wxs template to use.", "type": ["string", "null"]}, "fragmentPaths": {"description": "A list of paths to .wxs files with WiX fragments to use.", "default": [], "type": "array", "items": {"type": "string"}}, "componentGroupRefs": {"description": "The ComponentGroup element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "componentRefs": {"description": "The Component element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "featureGroupRefs": {"description": "The FeatureGroup element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "featureRefs": {"description": "The Feature element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "mergeRefs": {"description": "The Merge element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "skipWebviewInstall": {"description": "Disables the Webview2 runtime installation after app install.\n\nWill be removed in v2, prefer the [`WindowsConfig::webview_install_mode`] option.", "default": false, "type": "boolean"}, "license": {"description": "The path to the license file to render on the installer.\n\nMust be an RTF file, so if a different extension is provided, we convert it to the RTF format.", "type": ["string", "null"]}, "enableElevatedUpdateTask": {"description": "Create an elevated update task within Windows Task Scheduler.", "default": false, "type": "boolean"}, "bannerPath": {"description": "Path to a bitmap file to use as the installation user interface banner. This bitmap will appear at the top of all but the first page of the installer.\n\nThe required dimensions are 493px × 58px.", "type": ["string", "null"]}, "dialogImagePath": {"description": "Path to a bitmap file to use on the installation user interface dialogs. It is used on the welcome and completion dialogs. The required dimensions are 493px × 312px.", "type": ["string", "null"]}}, "additionalProperties": false}, "WixLanguage": {"description": "The languages to build using WiX.", "anyOf": [{"description": "A single language to build, without configuration.", "type": "string"}, {"description": "A list of languages to build, without configuration.", "type": "array", "items": {"type": "string"}}, {"description": "A map of languages and its configuration.", "type": "object", "additionalProperties": {"$ref": "#/definitions/WixLanguageConfig"}}]}, "WixLanguageConfig": {"description": "Configuration for a target language for the WiX build.\n\nSee more: https://tauri.app/v1/api/config#wixlanguageconfig", "type": "object", "properties": {"localePath": {"description": "The path to a locale (`.wxl`) file. See <https://wixtoolset.org/documentation/manual/v3/howtos/ui_and_localization/build_a_localized_version.html>.", "type": ["string", "null"]}}, "additionalProperties": false}, "NsisConfig": {"description": "Configuration for the Installer bundle using NSIS.", "type": "object", "properties": {"template": {"description": "A custom .nsi template to use.", "type": ["string", "null"]}, "license": {"description": "The path to the license file to render on the installer.", "type": ["string", "null"]}, "headerImage": {"description": "The path to a bitmap file to display on the header of installers pages.\n\nThe recommended dimensions are 150px x 57px.", "type": ["string", "null"]}, "sidebarImage": {"description": "The path to a bitmap file for the Welcome page and the Finish page.\n\nThe recommended dimensions are 164px x 314px.", "type": ["string", "null"]}, "installerIcon": {"description": "The path to an icon file used as the installer icon.", "type": ["string", "null"]}, "installMode": {"description": "Whether the installation will be for all users or just the current user.", "default": "currentUser", "allOf": [{"$ref": "#/definitions/NSISInstallerMode"}]}, "languages": {"description": "A list of installer languages. By default the OS language is used. If the OS language is not in the list of languages, the first language will be used. To allow the user to select the language, set `display_language_selector` to `true`.\n\nSee <https://github.com/kichik/nsis/tree/9465c08046f00ccb6eda985abbdbf52c275c6c4d/Contrib/Language%20files> for the complete list of languages.", "type": ["array", "null"], "items": {"type": "string"}}, "customLanguageFiles": {"description": "A key-value pair where the key is the language and the value is the path to a custom `.nsh` file that holds the translated text for ta<PERSON>'s custom messages.\n\nSee <https://github.com/tauri-apps/tauri/blob/dev/tooling/bundler/src/bundle/windows/templates/nsis-languages/English.nsh> for an example `.nsh` file.\n\n**Note**: the key must be a valid NSIS language and it must be added to [`NsisConfig`] languages array,", "type": ["object", "null"], "additionalProperties": {"type": "string"}}, "displayLanguageSelector": {"description": "Whether to display a language selector dialog before the installer and uninstaller windows are rendered or not. By default the OS language is selected, with a fallback to the first language in the `languages` array.", "default": false, "type": "boolean"}, "compression": {"description": "Set the compression algorithm used to compress files in the installer.\n\nSee <https://nsis.sourceforge.io/Reference/SetCompressor>", "anyOf": [{"$ref": "#/definitions/NsisCompression"}, {"type": "null"}]}}, "additionalProperties": false}, "NSISInstallerMode": {"description": "Install Modes for the NSIS installer.", "oneOf": [{"description": "Default mode for the installer.\n\nInstall the app by default in a directory that doesn't require Administrator access.\n\nInstaller metadata will be saved under the `HKCU` registry path.", "type": "string", "enum": ["currentUser"]}, {"description": "Install the app by default in the `Program Files` folder directory requires Administrator access for the installation.\n\nInstaller metadata will be saved under the `HKLM` registry path.", "type": "string", "enum": ["perMachine"]}, {"description": "Combines both modes and allows the user to choose at install time whether to install for the current user or per machine. Note that this mode will require Administrator access even if the user wants to install it for the current user only.\n\nInstaller metadata will be saved under the `HKLM` or `HKCU` registry path based on the user's choice.", "type": "string", "enum": ["both"]}]}, "NsisCompression": {"description": "Compression algorithms used in the NSIS installer.\n\nSee <https://nsis.sourceforge.io/Reference/SetCompressor>", "oneOf": [{"description": "ZLIB uses the deflate algorithm, it is a quick and simple method. With the default compression level it uses about 300 KB of memory.", "type": "string", "enum": ["zlib"]}, {"description": "BZIP2 usually gives better compression ratios than ZLIB, but it is a bit slower and uses more memory. With the default compression level it uses about 4 MB of memory.", "type": "string", "enum": ["bzip2"]}, {"description": "LZMA (default) is a new compression method that gives very good compression ratios. The decompression speed is high (10-20 MB/s on a 2 GHz CPU), the compression speed is lower. The memory size that will be used for decompression is the dictionary size plus a few KBs, the default is 8 MB.", "type": "string", "enum": ["lzma"]}]}, "AllowlistConfig": {"description": "Allowlist configuration. The allowlist is a translation of the [Cargo allowlist features](https://docs.rs/tauri/latest/tauri/#cargo-allowlist-features).\n\n# Notes\n\n- Endpoints that don't have their own allowlist option are enabled by default. - There is only \"opt-in\", no \"opt-out\". Setting an option to `false` has no effect.\n\n# Examples\n\n- * [`\"app-all\": true`](https://tauri.app/v1/api/config/#appallowlistconfig.all) will make the [hide](https://tauri.app/v1/api/js/app#hide) endpoint be available regardless of whether `hide` is set to `false` or `true` in the allowlist.", "type": "object", "properties": {"all": {"description": "Use this flag to enable all API features.", "default": false, "type": "boolean"}, "fs": {"description": "File system API allowlist.", "default": {"all": false, "copyFile": false, "createDir": false, "exists": false, "readDir": false, "readFile": false, "removeDir": false, "removeFile": false, "renameFile": false, "scope": [], "writeFile": false}, "allOf": [{"$ref": "#/definitions/FsAllowlistConfig"}]}, "window": {"description": "Window API allowlist.", "default": {"all": false, "center": false, "close": false, "create": false, "hide": false, "maximize": false, "minimize": false, "print": false, "requestUserAttention": false, "setAlwaysOnTop": false, "setClosable": false, "setContentProtected": false, "setCursorGrab": false, "setCursorIcon": false, "setCursorPosition": false, "setCursorVisible": false, "setDecorations": false, "setFocus": false, "setFullscreen": false, "setIcon": false, "setIgnoreCursorEvents": false, "setMaxSize": false, "setMaximizable": false, "setMinSize": false, "setMinimizable": false, "setPosition": false, "setResizable": false, "setSize": false, "setSkipTaskbar": false, "setTitle": false, "show": false, "startDragging": false, "unmaximize": false, "unminimize": false}, "allOf": [{"$ref": "#/definitions/WindowAllowlistConfig"}]}, "shell": {"description": "Shell API allowlist.", "default": {"all": false, "execute": false, "open": false, "scope": [], "sidecar": false}, "allOf": [{"$ref": "#/definitions/ShellAllowlistConfig"}]}, "dialog": {"description": "Dialog API allowlist.", "default": {"all": false, "ask": false, "confirm": false, "message": false, "open": false, "save": false}, "allOf": [{"$ref": "#/definitions/DialogAllowlistConfig"}]}, "http": {"description": "HTTP API allowlist.", "default": {"all": false, "request": false, "scope": []}, "allOf": [{"$ref": "#/definitions/HttpAllowlistConfig"}]}, "notification": {"description": "Notification API allowlist.", "default": {"all": false}, "allOf": [{"$ref": "#/definitions/NotificationAllowlistConfig"}]}, "globalShortcut": {"description": "Global shortcut API allowlist.", "default": {"all": false}, "allOf": [{"$ref": "#/definitions/GlobalShortcutAllowlistConfig"}]}, "os": {"description": "OS allowlist.", "default": {"all": false}, "allOf": [{"$ref": "#/definitions/OsAllowlistConfig"}]}, "path": {"description": "Path API allowlist.", "default": {"all": false}, "allOf": [{"$ref": "#/definitions/PathAllowlistConfig"}]}, "protocol": {"description": "Custom protocol allowlist.", "default": {"all": false, "asset": false, "assetScope": []}, "allOf": [{"$ref": "#/definitions/ProtocolAllowlistConfig"}]}, "process": {"description": "Process API allowlist.", "default": {"all": false, "exit": false, "relaunch": false, "relaunchDangerousAllowSymlinkMacos": false}, "allOf": [{"$ref": "#/definitions/ProcessAllowlistConfig"}]}, "clipboard": {"description": "Clipboard APIs allowlist.", "default": {"all": false, "readText": false, "writeText": false}, "allOf": [{"$ref": "#/definitions/ClipboardAllowlistConfig"}]}, "app": {"description": "App APIs allowlist.", "default": {"all": false, "hide": false, "show": false}, "allOf": [{"$ref": "#/definitions/AppAllowlistConfig"}]}}, "additionalProperties": false}, "FsAllowlistConfig": {"description": "Allowlist for the file system APIs.\n\nSee more: https://tauri.app/v1/api/config#fsallowlistconfig", "type": "object", "properties": {"scope": {"description": "The access scope for the filesystem APIs.", "default": [], "allOf": [{"$ref": "#/definitions/FsAllowlistScope"}]}, "all": {"description": "Use this flag to enable all file system API features.", "default": false, "type": "boolean"}, "readFile": {"description": "Read file from local filesystem.", "default": false, "type": "boolean"}, "writeFile": {"description": "Write file to local filesystem.", "default": false, "type": "boolean"}, "readDir": {"description": "Read directory from local filesystem.", "default": false, "type": "boolean"}, "copyFile": {"description": "Copy file from local filesystem.", "default": false, "type": "boolean"}, "createDir": {"description": "Create directory from local filesystem.", "default": false, "type": "boolean"}, "removeDir": {"description": "Remove directory from local filesystem.", "default": false, "type": "boolean"}, "removeFile": {"description": "Remove file from local filesystem.", "default": false, "type": "boolean"}, "renameFile": {"description": "Rename file from local filesystem.", "default": false, "type": "boolean"}, "exists": {"description": "Check if path exists on the local filesystem.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "FsAllowlistScope": {"description": "Filesystem scope definition. It is a list of glob patterns that restrict the API access from the webview.\n\nEach pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "anyOf": [{"description": "A list of paths that are allowed by this scope.", "type": "array", "items": {"type": "string"}}, {"description": "A complete scope configuration.", "type": "object", "properties": {"allow": {"description": "A list of paths that are allowed by this scope.", "default": [], "type": "array", "items": {"type": "string"}}, "deny": {"description": "A list of paths that are not allowed by this scope. This gets precedence over the [`Self::Scope::allow`] list.", "default": [], "type": "array", "items": {"type": "string"}}, "requireLiteralLeadingDot": {"description": "Whether or not paths that contain components that start with a `.` will require that `.` appears literally in the pattern; `*`, `?`, `**`, or `[...]` will not match. This is useful because such files are conventionally considered hidden on Unix systems and it might be desirable to skip them when listing files.\n\nDefaults to `true` on Unix systems and `false` on Windows", "type": ["boolean", "null"]}}}]}, "WindowAllowlistConfig": {"description": "Allowlist for the window APIs.\n\nSee more: https://tauri.app/v1/api/config#windowallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all window API features.", "default": false, "type": "boolean"}, "create": {"description": "Allows dynamic window creation.", "default": false, "type": "boolean"}, "center": {"description": "Allows centering the window.", "default": false, "type": "boolean"}, "requestUserAttention": {"description": "Allows requesting user attention on the window.", "default": false, "type": "boolean"}, "setResizable": {"description": "Allows setting the resizable flag of the window.", "default": false, "type": "boolean"}, "setMaximizable": {"description": "Allows setting whether the window's native maximize button is enabled or not.", "default": false, "type": "boolean"}, "setMinimizable": {"description": "Allows setting whether the window's native minimize button is enabled or not.", "default": false, "type": "boolean"}, "setClosable": {"description": "Allows setting whether the window's native close button is enabled or not.", "default": false, "type": "boolean"}, "setTitle": {"description": "Allows changing the window title.", "default": false, "type": "boolean"}, "maximize": {"description": "Allows maximizing the window.", "default": false, "type": "boolean"}, "unmaximize": {"description": "Allows unmaximizing the window.", "default": false, "type": "boolean"}, "minimize": {"description": "Allows minimizing the window.", "default": false, "type": "boolean"}, "unminimize": {"description": "Allows unminimizing the window.", "default": false, "type": "boolean"}, "show": {"description": "Allows showing the window.", "default": false, "type": "boolean"}, "hide": {"description": "Allows hiding the window.", "default": false, "type": "boolean"}, "close": {"description": "Allows closing the window.", "default": false, "type": "boolean"}, "setDecorations": {"description": "Allows setting the decorations flag of the window.", "default": false, "type": "boolean"}, "setAlwaysOnTop": {"description": "Allows setting the always_on_top flag of the window.", "default": false, "type": "boolean"}, "setContentProtected": {"description": "Allows preventing the window contents from being captured by other apps.", "default": false, "type": "boolean"}, "setSize": {"description": "Allows setting the window size.", "default": false, "type": "boolean"}, "setMinSize": {"description": "Allows setting the window minimum size.", "default": false, "type": "boolean"}, "setMaxSize": {"description": "Allows setting the window maximum size.", "default": false, "type": "boolean"}, "setPosition": {"description": "Allows changing the position of the window.", "default": false, "type": "boolean"}, "setFullscreen": {"description": "Allows setting the fullscreen flag of the window.", "default": false, "type": "boolean"}, "setFocus": {"description": "Allows focusing the window.", "default": false, "type": "boolean"}, "setIcon": {"description": "Allows changing the window icon.", "default": false, "type": "boolean"}, "setSkipTaskbar": {"description": "Allows setting the skip_taskbar flag of the window.", "default": false, "type": "boolean"}, "setCursorGrab": {"description": "Allows grabbing the cursor.", "default": false, "type": "boolean"}, "setCursorVisible": {"description": "Allows setting the cursor visibility.", "default": false, "type": "boolean"}, "setCursorIcon": {"description": "Allows changing the cursor icon.", "default": false, "type": "boolean"}, "setCursorPosition": {"description": "Allows setting the cursor position.", "default": false, "type": "boolean"}, "setIgnoreCursorEvents": {"description": "Allows ignoring cursor events.", "default": false, "type": "boolean"}, "startDragging": {"description": "Allows start dragging on the window.", "default": false, "type": "boolean"}, "print": {"description": "Allows opening the system dialog to print the window content.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "ShellAllowlistConfig": {"description": "Allowlist for the shell APIs.\n\nSee more: https://tauri.app/v1/api/config#shellallowlistconfig", "type": "object", "properties": {"scope": {"description": "Access scope for the binary execution APIs. Sidecars are automatically enabled.", "default": [], "allOf": [{"$ref": "#/definitions/ShellAllowlistScope"}]}, "all": {"description": "Use this flag to enable all shell API features.", "default": false, "type": "boolean"}, "execute": {"description": "Enable binary execution.", "default": false, "type": "boolean"}, "sidecar": {"description": "Enable sidecar execution, allowing the JavaScript layer to spawn a sidecar command, an executable that is shipped with the application. For more information see <https://tauri.app/v1/guides/building/sidecar>.", "default": false, "type": "boolean"}, "open": {"description": "Open URL with the user's default application.", "default": false, "allOf": [{"$ref": "#/definitions/ShellAllowlistOpen"}]}}, "additionalProperties": false}, "ShellAllowlistScope": {"description": "Shell scope definition. It is a list of command names and associated CLI arguments that restrict the API access from the webview.", "type": "array", "items": {"$ref": "#/definitions/ShellAllowedCommand"}}, "ShellAllowedCommand": {"description": "A command allowed to be executed by the webview API.", "type": "object", "required": ["name"], "properties": {"name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "default": "", "type": "string"}, "args": {"description": "The allowed arguments for the command execution.", "default": false, "allOf": [{"$ref": "#/definitions/ShellAllowedArgs"}]}, "sidecar": {"description": "If this command is a sidecar command.", "default": false, "type": "boolean"}}}, "ShellAllowedArgs": {"description": "A set of command arguments allowed to be executed by the webview API.\n\nA value of `true` will allow any arguments to be passed to the command. `false` will disable all arguments. A list of [`ShellAllowedArg`] will set those arguments as the only valid arguments to be passed to the attached command configuration.", "anyOf": [{"description": "Use a simple boolean to allow all or disable all arguments to this command configuration.", "type": "boolean"}, {"description": "A specific set of [`ShellAllowedArg`] that are valid to call for the command configuration.", "type": "array", "items": {"$ref": "#/definitions/ShellAllowedArg"}}]}, "ShellAllowedArg": {"description": "A command argument allowed to be executed by the webview API.", "anyOf": [{"description": "A non-configurable argument that is passed to the command in the order it was specified.", "type": "string"}, {"description": "A variable that is set while calling the command from the webview API.", "type": "object", "required": ["validator"], "properties": {"validator": {"description": "[regex] validator to require passed values to conform to an expected input.\n\nThis will require the argument value passed to this variable to match the `validator` regex before it will be executed.\n\n[regex]: https://docs.rs/regex/latest/regex/#syntax", "type": "string"}}, "additionalProperties": false}]}, "ShellAllowlistOpen": {"description": "Defines the `shell > open` api scope.", "anyOf": [{"description": "If the shell open API should be enabled.\n\nIf enabled, the default validation regex (`^((mailto:\\w+)|(tel:\\w+)|(https?://\\w+)).+`) is used.", "type": "boolean"}, {"description": "Enable the shell open API, with a custom regex that the opened path must match against.\n\nIf using a custom regex to support a non-http(s) schema, care should be used to prevent values that allow flag-like strings to pass validation. e.g. `--enable-debugging`, `-i`, `/R`.", "type": "string"}]}, "DialogAllowlistConfig": {"description": "Allowlist for the dialog APIs.\n\nSee more: https://tauri.app/v1/api/config#dialogallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all dialog API features.", "default": false, "type": "boolean"}, "open": {"description": "Allows the API to open a dialog window to pick files.", "default": false, "type": "boolean"}, "save": {"description": "Allows the API to open a dialog window to pick where to save files.", "default": false, "type": "boolean"}, "message": {"description": "Allows the API to show a message dialog window.", "default": false, "type": "boolean"}, "ask": {"description": "Allows the API to show a dialog window with Yes/No buttons.", "default": false, "type": "boolean"}, "confirm": {"description": "Allows the API to show a dialog window with Ok/Cancel buttons.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "HttpAllowlistConfig": {"description": "Allowlist for the HTTP APIs.\n\nSee more: https://tauri.app/v1/api/config#httpallowlistconfig", "type": "object", "properties": {"scope": {"description": "The access scope for the HTTP APIs.", "default": [], "allOf": [{"$ref": "#/definitions/HttpAllowlistScope"}]}, "all": {"description": "Use this flag to enable all HTTP API features.", "default": false, "type": "boolean"}, "request": {"description": "Allows making HTTP requests.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "HttpAllowlistScope": {"description": "HTTP API scope definition. It is a list of URLs that can be accessed by the webview when using the HTTP APIs. The scoped URL is matched against the request URL using a glob pattern.\n\nExamples: - \"https://*\": allows all HTTPS urls - \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path - \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "array", "items": {"type": "string", "format": "uri"}}, "NotificationAllowlistConfig": {"description": "Allowlist for the notification APIs.\n\nSee more: https://tauri.app/v1/api/config#notificationallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all notification API features.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "GlobalShortcutAllowlistConfig": {"description": "Allowlist for the global shortcut APIs.\n\nSee more: https://tauri.app/v1/api/config#globalshortcutallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all global shortcut API features.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "OsAllowlistConfig": {"description": "Allowlist for the OS APIs.\n\nSee more: https://tauri.app/v1/api/config#osallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all OS API features.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "PathAllowlistConfig": {"description": "Allowlist for the path APIs.\n\nSee more: https://tauri.app/v1/api/config#pathallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all path API features.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "ProtocolAllowlistConfig": {"description": "Allowlist for the custom protocols.\n\nSee more: https://tauri.app/v1/api/config#protocolallowlistconfig", "type": "object", "properties": {"assetScope": {"description": "The access scope for the asset protocol.", "default": [], "allOf": [{"$ref": "#/definitions/FsAllowlistScope"}]}, "all": {"description": "Use this flag to enable all custom protocols.", "default": false, "type": "boolean"}, "asset": {"description": "Enables the asset protocol.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "ProcessAllowlistConfig": {"description": "Allowlist for the process APIs.\n\nSee more: https://tauri.app/v1/api/config#processallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all process APIs.", "default": false, "type": "boolean"}, "relaunch": {"description": "Enables the relaunch API.", "default": false, "type": "boolean"}, "relaunchDangerousAllowSymlinkMacos": {"description": "Dangerous option that allows macOS to relaunch even if the binary contains a symlink.\n\nThis is due to macOS having less symlink protection. Highly recommended to not set this flag unless you have a very specific reason too, and understand the implications of it.", "default": false, "type": "boolean"}, "exit": {"description": "Enables the exit API.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "ClipboardAllowlistConfig": {"description": "Allowlist for the clipboard APIs.\n\nSee more: https://tauri.app/v1/api/config#clipboardallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all clipboard APIs.", "default": false, "type": "boolean"}, "writeText": {"description": "Enables the clipboard's `writeText` API.", "default": false, "type": "boolean"}, "readText": {"description": "Enables the clipboard's `readText` API.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "AppAllowlistConfig": {"description": "Allowlist for the app APIs.\n\nSee more: https://tauri.app/v1/api/config#appallowlistconfig", "type": "object", "properties": {"all": {"description": "Use this flag to enable all app APIs.", "default": false, "type": "boolean"}, "show": {"description": "Enables the app's `show` API.", "default": false, "type": "boolean"}, "hide": {"description": "Enables the app's `hide` API.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "SecurityConfig": {"description": "Security configuration.\n\nSee more: https://tauri.app/v1/api/config#securityconfig", "type": "object", "properties": {"csp": {"description": "The Content Security Policy that will be injected on all HTML files on the built application. If [`dev_csp`](#SecurityConfig.devCsp) is not specified, this value is also injected on dev.\n\nThis is a really important part of the configuration since it helps you ensure your WebView is secured. See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"$ref": "#/definitions/Csp"}, {"type": "null"}]}, "devCsp": {"description": "The Content Security Policy that will be injected on all HTML files on development.\n\nThis is a really important part of the configuration since it helps you ensure your WebView is secured. See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"$ref": "#/definitions/Csp"}, {"type": "null"}]}, "freezePrototype": {"description": "Freeze the `Object.prototype` when using the custom protocol.", "default": false, "type": "boolean"}, "dangerousDisableAssetCspModification": {"description": "Disables the Tauri-injected CSP sources.\n\nAt compile time, <PERSON><PERSON> parses all the frontend assets and changes the Content-Security-Policy to only allow loading of your own scripts and styles by injecting nonce and hash sources. This stricts your CSP, which may introduce issues when using along with other flexing sources.\n\nThis configuration option allows both a boolean and a list of strings as value. A boolean instructs <PERSON><PERSON> to disable the injection for all CSP injections, and a list of strings indicates the CSP directives that <PERSON><PERSON> cannot inject.\n\n**WARNING:** Only disable this if you know what you are doing and have properly configured the CSP. Your application might be vulnerable to XSS attacks without this <PERSON><PERSON> protection.", "default": false, "allOf": [{"$ref": "#/definitions/DisabledCspModificationKind"}]}, "dangerousRemoteDomainIpcAccess": {"description": "Allow external domains to send command to <PERSON><PERSON>.\n\nBy default, external domains do not have access to `window.__TAURI__`, which means they cannot communicate with the commands defined in Rust. This prevents attacks where an externally loaded malicious or compromised sites could start executing commands on the user's device.\n\nThis configuration allows a set of external domains to have access to the Tauri commands. When you configure a domain to be allowed to access the IPC, all subpaths are allowed. Subdomains are not allowed.\n\n**WARNING:** Only use this option if you either have internal checks against malicious external sites or you can trust the allowed external sites. You application might be vulnerable to dangerous Tauri command related attacks otherwise.", "default": [], "type": "array", "items": {"$ref": "#/definitions/RemoteDomainAccessScope"}}, "dangerousUseHttpScheme": {"description": "Sets whether the custom protocols should use `http://<scheme>.localhost` instead of the default `https://<scheme>.localhost` on Windows.\n\n**WARNING:** Using a `http` scheme will allow mixed content when trying to fetch `http` endpoints and is therefore less secure but will match the behavior of the `<scheme>://localhost` protocols used on macOS and Linux.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "Csp": {"description": "A Content-Security-Policy definition. See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"description": "The entire CSP policy in a single text string.", "type": "string"}, {"description": "An object mapping a directive with its sources values as a list of strings.", "type": "object", "additionalProperties": {"$ref": "#/definitions/CspDirectiveSources"}}]}, "CspDirectiveSources": {"description": "A Content-Security-Policy directive source list. See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/Sources#sources>.", "anyOf": [{"description": "An inline list of CSP sources. Same as [`Self::List`], but concatenated with a space separator.", "type": "string"}, {"description": "A list of CSP sources. The collection will be concatenated with a space separator for the CSP string.", "type": "array", "items": {"type": "string"}}]}, "DisabledCspModificationKind": {"description": "The possible values for the `dangerous_disable_asset_csp_modification` config option.", "anyOf": [{"description": "If `true`, disables all CSP modification. `false` is the default value and it configures <PERSON><PERSON> to control the CSP.", "type": "boolean"}, {"description": "Disables the given list of CSP directives modifications.", "type": "array", "items": {"type": "string"}}]}, "RemoteDomainAccessScope": {"description": "External command access definition.", "type": "object", "required": ["domain", "windows"], "properties": {"scheme": {"description": "The URL scheme to allow. By default, all schemas are allowed.", "type": ["string", "null"]}, "domain": {"description": "The domain to allow.", "type": "string"}, "windows": {"description": "The list of window labels this scope applies to.", "type": "array", "items": {"type": "string"}}, "plugins": {"description": "The list of plugins that are allowed in this scope. The names should be without the `tauri-plugin-` prefix, for example `\"store\"` for `tauri-plugin-store`.", "default": [], "type": "array", "items": {"type": "string"}}, "enableTauriAPI": {"description": "Enables access to the Tauri API.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "UpdaterConfig": {"description": "The Updater configuration object.\n\nSee more: https://tauri.app/v1/api/config#updaterconfig", "type": "object", "properties": {"active": {"description": "Whether the updater is active or not.", "default": false, "type": "boolean"}, "dialog": {"description": "Display built-in dialog or use event system if disabled.", "default": true, "type": "boolean"}, "endpoints": {"description": "The updater endpoints. TLS is enforced on production.\n\nThe updater URL can contain the following variables: - {{current_version}}: The version of the app that is requesting the update - {{target}}: The operating system name (one of `linux`, `windows` or `darwin`). - {{arch}}: The architecture of the machine (one of `x86_64`, `i686`, `aarch64` or `armv7`).\n\n# Examples - \"https://my.cdn.com/latest.json\": a raw JSON endpoint that returns the latest version and download links for each platform. - \"https://updates.app.dev/{{target}}?version={{current_version}}&arch={{arch}}\": a dedicated API with positional and query string arguments.", "type": ["array", "null"], "items": {"$ref": "#/definitions/UpdaterEndpoint"}}, "pubkey": {"description": "Signature public key.", "default": "", "type": "string"}, "windows": {"description": "The Windows configuration for the updater.", "default": {"installMode": "passive", "installerArgs": []}, "allOf": [{"$ref": "#/definitions/UpdaterWindowsConfig"}]}}, "additionalProperties": false}, "UpdaterEndpoint": {"description": "A URL to an updater server.\n\nThe URL must use the `https` scheme on production.", "type": "string", "format": "uri"}, "UpdaterWindowsConfig": {"description": "The updater configuration for Windows.\n\nSee more: https://tauri.app/v1/api/config#updaterwindowsconfig", "type": "object", "properties": {"installerArgs": {"description": "Additional arguments given to the NSIS or WiX installer.", "default": [], "type": "array", "items": {"type": "string"}}, "installMode": {"description": "The installation mode for the update on Windows. Defaults to `passive`.", "default": "passive", "allOf": [{"$ref": "#/definitions/WindowsUpdateInstallMode"}]}}, "additionalProperties": false}, "WindowsUpdateInstallMode": {"description": "Install modes for the Windows update.", "oneOf": [{"description": "Specifies there's a basic UI during the installation process, including a final dialog box at the end.", "type": "string", "enum": ["basicUi"]}, {"description": "The quiet mode means there's no user interaction required. Requires admin privileges if the installer does.", "type": "string", "enum": ["quiet"]}, {"description": "Specifies unattended mode, which means the installation only shows a progress bar.", "type": "string", "enum": ["passive"]}]}, "SystemTrayConfig": {"description": "Configuration for application system tray icon.\n\nSee more: https://tauri.app/v1/api/config#systemtrayconfig", "type": "object", "required": ["iconPath"], "properties": {"iconPath": {"description": "Path to the default icon to use on the system tray.", "type": "string"}, "iconAsTemplate": {"description": "A Boolean value that determines whether the image represents a [template](https://developer.apple.com/documentation/appkit/nsimage/1520017-template?language=objc) image on macOS.", "default": false, "type": "boolean"}, "menuOnLeftClick": {"description": "A Boolean value that determines whether the menu should appear when the tray icon receives a left click on macOS.", "default": true, "type": "boolean"}, "title": {"description": "Title for MacOS tray", "type": ["string", "null"]}}, "additionalProperties": false}, "BuildConfig": {"description": "The Build configuration object.\n\nSee more: https://tauri.app/v1/api/config#buildconfig", "type": "object", "properties": {"runner": {"description": "The binary used to build and run the application.", "type": ["string", "null"]}, "devPath": {"description": "The path to the application assets or URL to load in development.\n\nThis is usually an URL to a dev server, which serves your application assets with live reloading. Most modern JavaScript bundlers provides a way to start a dev server by default.\n\nSee [vite](https://vitejs.dev/guide/), [Webpack DevServer](https://webpack.js.org/configuration/dev-server/) and [sirv](https://github.com/lukeed/sirv) for examples on how to set up a dev server.", "default": "http://localhost:8080/", "allOf": [{"$ref": "#/definitions/AppUrl"}]}, "distDir": {"description": "The path to the application assets or URL to load in production.\n\nWhen a path relative to the configuration file is provided, it is read recursively and all files are embedded in the application binary. <PERSON><PERSON> then looks for an `index.html` file unless you provide a custom window URL.\n\nYou can also provide a list of paths to be embedded, which allows granular control over what files are added to the binary. In this case, all files are added to the root and you must reference it that way in your HTML files.\n\nWhen an URL is provided, the application won't have bundled assets and the application will load that URL by default.", "default": "../dist", "allOf": [{"$ref": "#/definitions/AppUrl"}]}, "beforeDevCommand": {"description": "A shell command to run before `tauri dev` kicks in.\n\nThe TAURI_PLATFORM, TAURI_ARCH, TAURI_FAMILY, TAURI_PLATFORM_VERSION, TAURI_PLATFORM_TYPE and TAURI_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/BeforeDevCommand"}, {"type": "null"}]}, "beforeBuildCommand": {"description": "A shell command to run before `tauri build` kicks in.\n\nThe TAURI_PLATFORM, TAURI_ARCH, TAURI_FAMILY, TAURI_PLATFORM_VERSION, TAURI_PLATFORM_TYPE and TAURI_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/HookCommand"}, {"type": "null"}]}, "beforeBundleCommand": {"description": "A shell command to run before the bundling phase in `tauri build` kicks in.\n\nThe TAURI_PLATFORM, TAURI_ARCH, TAURI_FAMILY, TAURI_PLATFORM_VERSION, TAURI_PLATFORM_TYPE and TAURI_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/HookCommand"}, {"type": "null"}]}, "features": {"description": "Features passed to `cargo` commands.", "type": ["array", "null"], "items": {"type": "string"}}, "withGlobalTauri": {"description": "Whether we should inject the Tauri API on `window.__TAURI__` or not.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "AppUrl": {"description": "Defines the URL or assets to embed in the application.", "anyOf": [{"description": "The app's external URL, or the path to the directory containing the app assets.", "allOf": [{"$ref": "#/definitions/WindowUrl"}]}, {"description": "An array of files to embed on the app.", "type": "array", "items": {"type": "string"}}]}, "BeforeDevCommand": {"description": "Describes the shell command to run before `tauri dev`.", "anyOf": [{"description": "Run the given script with the default options.", "type": "string"}, {"description": "Run the given script with custom options.", "type": "object", "required": ["script"], "properties": {"script": {"description": "The script to execute.", "type": "string"}, "cwd": {"description": "The current working directory.", "type": ["string", "null"]}, "wait": {"description": "Whether `tauri dev` should wait for the command to finish or not. Defaults to `false`.", "default": false, "type": "boolean"}}}]}, "HookCommand": {"description": "Describes a shell command to be executed when a CLI hook is triggered.", "anyOf": [{"description": "Run the given script with the default options.", "type": "string"}, {"description": "Run the given script with custom options.", "type": "object", "required": ["script"], "properties": {"script": {"description": "The script to execute.", "type": "string"}, "cwd": {"description": "The current working directory.", "type": ["string", "null"]}}}]}, "PluginConfig": {"description": "The plugin configs holds a HashMap mapping a plugin name to its configuration object.\n\nSee more: https://tauri.app/v1/api/config#pluginconfig", "type": "object", "additionalProperties": true}}}