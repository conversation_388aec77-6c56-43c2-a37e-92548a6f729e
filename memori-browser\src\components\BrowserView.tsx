﻿import React from 'react';
import { useBrowserStore } from '../stores/browserStore';

export interface BrowserViewProps {
  className?: string;
}

export const BrowserView: React.FC<BrowserViewProps> = ({ className = '' }) => {
  const { tabs, activeTabId } = useBrowserStore();
  const activeTab = tabs.find(tab => tab.id === activeTabId);

  if (!activeTab) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 dark:bg-gray-900 ${className}`}>
        <div className="text-center">
          <div className="text-6xl mb-4">🧠</div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-4">
            Welcome to Memori Browser
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            AI-enhanced browsing with ultra-lightweight performance
          </p>
          <div className="text-sm text-gray-500 dark:text-gray-500">
            Create a new tab to start browsing
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full bg-white dark:bg-gray-900 ${className}`}>
      {/* Content area - in a real implementation, this would be a webview */}
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="text-4xl mb-4">🌐</div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
            {activeTab.title}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4 break-all">
            {activeTab.url}
          </p>
          {activeTab.isLoading && (
            <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">Loading...</span>
            </div>
          )}
          <div className="mt-6 text-sm text-gray-500 dark:text-gray-500">
            <p>🚧 Webview integration coming soon!</p>
            <p className="mt-2">This will render the actual web content.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Utility functions for browser view management
export const browserViewUtils = {
  // Placeholder for future webview utilities
  attachWebview: (tabId: string, element: HTMLElement) => {
    console.log('Attaching webview for tab:', tabId, 'to element:', element);
  },

  detachWebview: (tabId: string) => {
    console.log('Detaching webview for tab:', tabId);
  },

  resizeWebview: (tabId: string, width: number, height: number) => {
    console.log('Resizing webview for tab:', tabId, 'to:', width, 'x', height);
  },
};
