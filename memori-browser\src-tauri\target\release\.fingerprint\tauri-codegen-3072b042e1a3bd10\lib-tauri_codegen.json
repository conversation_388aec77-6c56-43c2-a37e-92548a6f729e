{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 18439556900360439042, "deps": [[3060637413840920116, "proc_macro2", false, 10231316123642883689], [4899080583175475170, "semver", false, 1549203043095575051], [7392050791754369441, "ico", false, 15949160460862288265], [8008191657135824715, "thiserror", false, 1877602138914161519], [8292277814562636972, "tauri_utils", false, 312666523984760491], [8319709847752024821, "uuid", false, 18145887820919119068], [9451456094439810778, "regex", false, 15637907173029174104], [9689903380558560274, "serde", false, 11335753460197236072], [9857275760291862238, "sha2", false, 3641410133999858153], [10301936376833819828, "json_patch", false, 9639229409941567450], [12687914511023397207, "png", false, 1678823655719435205], [14132538657330703225, "brotli", false, 17303557992878274585], [15367738274754116744, "serde_json", false, 17428953512726854418], [15622660310229662834, "walkdir", false, 9503943136891004439], [17990358020177143287, "quote", false, 18104525677915743884], [18066890886671768183, "base64", false, 11450659281162594371]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-3072b042e1a3bd10\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}