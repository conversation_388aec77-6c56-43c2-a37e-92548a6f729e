use std::collections::HashMap;
use std::sync::Arc;
use parking_lot::RwLock;
use tauri::{Manager, AppHandle};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TabInfo {
    pub id: String,
    pub url: String,
    pub title: String,
    pub favicon: Option<String>,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavigationEvent {
    pub tab_id: String,
    pub url: String,
    pub title: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

pub struct BrowserEngine {
    tabs: Arc<RwLock<HashMap<String, Tab>>>,
    app_handle: AppHandle<tauri::Wry>,
}

struct Tab {
    id: String,
    url: String,
    title: String,
    history: Vec<String>,
    history_index: usize,
    is_loading: bool,
    created_at: chrono::DateTime<chrono::Utc>,
}

impl BrowserEngine {
    pub fn new(app_handle: AppHandle<tauri::Wry>) -> Self {
        Self {
            tabs: Arc::new(RwLock::new(HashMap::new())),
            app_handle,
        }
    }

    pub async fn create_tab(&self, url: &str) -> Result<TabInfo, String> {
        let tab_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();
        
        // For now, we'll create a logical tab without a separate window
        // In a full implementation, this would create a webview window
        let tab = Tab {
            id: tab_id.clone(),
            url: url.to_string(),
            title: "New Tab".to_string(),
            history: vec![url.to_string()],
            history_index: 0,
            is_loading: false,
            created_at: now,
        };

        let tab_info = TabInfo {
            id: tab_id.clone(),
            url: url.to_string(),
            title: tab.title.clone(),
            favicon: None,
            is_loading: false,
            can_go_back: false,
            can_go_forward: false,
            created_at: now,
        };

        self.tabs.write().insert(tab_id.clone(), tab);
        
        // Emit tab created event
        let _ = self.app_handle.emit_all("tab-created", &tab_info);
        
        Ok(tab_info)
    }

    pub async fn navigate(&self, tab_id: &str, url: &str) -> Result<(), String> {
        let mut tabs = self.tabs.write();
        let tab = tabs.get_mut(tab_id)
            .ok_or_else(|| "Tab not found".to_string())?;

        // Update history
        tab.history.truncate(tab.history_index + 1);
        tab.history.push(url.to_string());
        tab.history_index = tab.history.len() - 1;
        tab.url = url.to_string();
        tab.is_loading = true;

        // Emit navigation event
        let nav_event = NavigationEvent {
            tab_id: tab_id.to_string(),
            url: url.to_string(),
            title: tab.title.clone(),
            timestamp: chrono::Utc::now(),
        };
        
        let _ = self.app_handle.emit_all("navigation-start", &nav_event);

        // In a real implementation, this would navigate the webview
        // For now, we'll simulate navigation completion
        tokio::spawn({
            let app_handle = self.app_handle.clone();
            let nav_event = nav_event.clone();
            async move {
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                let _ = app_handle.emit_all("navigation-complete", &nav_event);
            }
        });

        Ok(())
    }

    pub async fn go_back(&self, tab_id: &str) -> Result<bool, String> {
        let mut tabs = self.tabs.write();
        let tab = tabs.get_mut(tab_id)
            .ok_or_else(|| "Tab not found".to_string())?;

        if tab.history_index > 0 {
            tab.history_index -= 1;
            let url = tab.history[tab.history_index].clone();
            tab.url = url.clone();
            
            let nav_event = NavigationEvent {
                tab_id: tab_id.to_string(),
                url,
                title: tab.title.clone(),
                timestamp: chrono::Utc::now(),
            };
            
            let _ = self.app_handle.emit_all("navigation-back", &nav_event);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub async fn go_forward(&self, tab_id: &str) -> Result<bool, String> {
        let mut tabs = self.tabs.write();
        let tab = tabs.get_mut(tab_id)
            .ok_or_else(|| "Tab not found".to_string())?;

        if tab.history_index < tab.history.len() - 1 {
            tab.history_index += 1;
            let url = tab.history[tab.history_index].clone();
            tab.url = url.clone();
            
            let nav_event = NavigationEvent {
                tab_id: tab_id.to_string(),
                url,
                title: tab.title.clone(),
                timestamp: chrono::Utc::now(),
            };
            
            let _ = self.app_handle.emit_all("navigation-forward", &nav_event);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub async fn refresh(&self, tab_id: &str) -> Result<(), String> {
        let tabs = self.tabs.read();
        let tab = tabs.get(tab_id)
            .ok_or_else(|| "Tab not found".to_string())?;

        let nav_event = NavigationEvent {
            tab_id: tab_id.to_string(),
            url: tab.url.clone(),
            title: tab.title.clone(),
            timestamp: chrono::Utc::now(),
        };
        
        let _ = self.app_handle.emit_all("navigation-refresh", &nav_event);
        Ok(())
    }

    pub async fn close_tab(&self, tab_id: &str) -> Result<(), String> {
        let mut tabs = self.tabs.write();
        if let Some(_tab) = tabs.remove(tab_id) {
            let _ = self.app_handle.emit_all("tab-closed", tab_id);
        }
        Ok(())
    }

    pub async fn get_all_tabs(&self) -> Result<Vec<TabInfo>, String> {
        let tabs = self.tabs.read();
        Ok(tabs.values().map(|tab| TabInfo {
            id: tab.id.clone(),
            url: tab.url.clone(),
            title: tab.title.clone(),
            favicon: None,
            is_loading: tab.is_loading,
            can_go_back: tab.history_index > 0,
            can_go_forward: tab.history_index < tab.history.len() - 1,
            created_at: tab.created_at,
        }).collect())
    }

    pub async fn get_tab(&self, tab_id: &str) -> Result<TabInfo, String> {
        let tabs = self.tabs.read();
        let tab = tabs.get(tab_id)
            .ok_or_else(|| "Tab not found".to_string())?;
            
        Ok(TabInfo {
            id: tab.id.clone(),
            url: tab.url.clone(),
            title: tab.title.clone(),
            favicon: None,
            is_loading: tab.is_loading,
            can_go_back: tab.history_index > 0,
            can_go_forward: tab.history_index < tab.history.len() - 1,
            created_at: tab.created_at,
        })
    }

    pub async fn update_tab_title(&self, tab_id: &str, title: &str) -> Result<(), String> {
        let mut tabs = self.tabs.write();
        if let Some(tab) = tabs.get_mut(tab_id) {
            tab.title = title.to_string();
            let _ = self.app_handle.emit_all("tab-title-updated", serde_json::json!({
                "tabId": tab_id,
                "title": title
            }));
        }
        Ok(())
    }
}

// Tauri commands
#[tauri::command]
pub async fn create_tab(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    url: String,
) -> Result<TabInfo, String> {
    state.create_tab(&url).await
}

#[tauri::command]
pub async fn navigate_to(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
    url: String,
) -> Result<(), String> {
    state.navigate(&tab_id, &url).await
}

#[tauri::command]
pub async fn go_back(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
) -> Result<bool, String> {
    state.go_back(&tab_id).await
}

#[tauri::command]
pub async fn go_forward(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
) -> Result<bool, String> {
    state.go_forward(&tab_id).await
}

#[tauri::command]
pub async fn refresh_tab(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
) -> Result<(), String> {
    state.refresh(&tab_id).await
}

#[tauri::command]
pub async fn close_tab(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
) -> Result<(), String> {
    state.close_tab(&tab_id).await
}

#[tauri::command]
pub async fn get_all_tabs(
    state: tauri::State<'_, Arc<BrowserEngine>>,
) -> Result<Vec<TabInfo>, String> {
    state.get_all_tabs().await
}

#[tauri::command]
pub async fn get_tab(
    state: tauri::State<'_, Arc<BrowserEngine>>,
    tab_id: String,
) -> Result<TabInfo, String> {
    state.get_tab(&tab_id).await
}
