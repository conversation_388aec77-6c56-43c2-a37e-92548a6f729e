{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10020888071089587331, "build_script_build", false, 5320354257391575256]], "local": [{"RerunIfChanged": {"output": "release\\build\\winapi-2c68798508155401\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}