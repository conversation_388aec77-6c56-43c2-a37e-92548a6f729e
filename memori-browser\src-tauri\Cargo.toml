[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
default-run = "app"
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.5", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.8.1", features = [ "http-all", "window-start-dragging", "window-set-position", "window-print", "window-minimize", "window-center", "window-set-cursor-position", "window-set-decorations", "window-show", "window-unmaximize", "window-set-cursor-visible", "window-set-min-size", "window-hide", "window-set-cursor-icon", "window-set-ignore-cursor-events", "window-set-always-on-top", "window-request-user-attention", "window-set-size", "window-set-cursor-grab", "window-set-focus", "window-unminimize", "window-set-title", "window-set-max-size", "window-close", "window-set-fullscreen", "window-maximize", "window-create", "window-set-resizable", "shell-open", "window-set-icon", "window-set-skip-taskbar"] }
open = "5.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]
