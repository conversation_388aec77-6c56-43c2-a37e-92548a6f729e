[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
default-run = "app"
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.5", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5", features = [ "window-set-icon", "window-create", "window-set-skip-taskbar", "window-close", "window-set-position", "window-request-user-attention", "window-center", "window-set-resizable", "window-set-decorations", "window-set-fullscreen", "window-set-focus", "shell-open", "window-unminimize", "window-set-cursor-visible", "window-start-dragging", "window-set-title", "window-set-min-size", "window-set-ignore-cursor-events", "window-set-cursor-position", "window-minimize", "window-maximize", "window-set-always-on-top", "http-all", "window-print", "window-unmaximize", "window-set-size", "window-show", "window-hide", "window-set-cursor-icon", "window-set-cursor-grab", "window-set-max-size"] }
open = "5.0"
# Additional dependencies for browser functionality
uuid = { version = "1.0", features = ["v4"] }
anyhow = "1.0"
parking_lot = "0.12"
tokio = { version = "1.0", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
url = "2.4"
regex = "1.10"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]
