﻿import React, { useState, useCallback, useEffect } from 'react';
import {
  ArrowLeft,
  ArrowRight,
  RotateCcw,
  Home,
  Star,
  Menu,
  Download,
  Bookmark
} from 'lucide-react';
import OptimizedTabBar from './components/OptimizedTabBar';
import { BrowserView } from './components/BrowserView';
import { SmartSearch } from './components/SmartSearch';
import { useBrowserStore } from './stores/browserStore';
import { cn } from './utils/cn';

const App: React.FC = () => {
  const [showBookmarks, setShowBookmarks] = useState(false);

  const {
    tabs,
    activeTabId,
    bookmarks,
    createTab,
    navigateTab,
    goBack,
    goForward,
    refreshTab,
    addBookmark,
    removeBookmark
  } = useBrowserStore();

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  // Create initial tab on mount
  useEffect(() => {
    if (tabs.length === 0) {
      createTab('https://www.google.com');
    }
  }, [tabs.length, createTab]);

  const handleNavigate = useCallback((url: string) => {
    if (activeTabId) {
      navigateTab(activeTabId, url);
    } else {
      createTab(url);
    }
  }, [activeTabId, navigateTab, createTab]);

  const handleBookmarkToggle = useCallback(() => {
    if (activeTab) {
      const existingBookmark = bookmarks.find(b => b.url === activeTab.url);
      if (existingBookmark) {
        removeBookmark(existingBookmark.id);
      } else {
        addBookmark(activeTab.url, activeTab.title);
      }
    }
  }, [activeTab, bookmarks, addBookmark, removeBookmark]);

  const isBookmarked = activeTab ? bookmarks.some(b => b.url === activeTab.url) : false;

  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      {/* Tab Bar */}
      <OptimizedTabBar />

      {/* Navigation Bar */}
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        {/* Navigation Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => activeTabId && goBack(activeTabId)}
            disabled={!activeTab?.canGoBack}
            className={cn(
              "p-2 rounded-full transition-colors",
              "hover:bg-gray-200 dark:hover:bg-gray-700",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
            title="Back"
          >
            <ArrowLeft className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={() => activeTabId && goForward(activeTabId)}
            disabled={!activeTab?.canGoForward}
            className={cn(
              "p-2 rounded-full transition-colors",
              "hover:bg-gray-200 dark:hover:bg-gray-700",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
            title="Forward"
          >
            <ArrowRight className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={() => activeTabId && refreshTab(activeTabId)}
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Refresh"
            disabled={!activeTab}
          >
            <RotateCcw className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={() => handleNavigate('https://google.com')}
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Home"
          >
            <Home className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Smart Search Bar */}
        <div className="flex-1 flex items-center">
          <div className="flex-1 max-w-2xl mx-auto">
            <SmartSearch />
          </div>
        </div>

        {/* Browser Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={handleBookmarkToggle}
            className={cn(
              "p-2 rounded-full transition-colors",
              isBookmarked
                ? "text-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                : "text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
            )}
            title={isBookmarked ? "Remove bookmark" : "Add bookmark"}
          >
            <Star className="w-4 h-4" fill={isBookmarked ? "currentColor" : "none"} />
          </button>

          <button
            onClick={() => setShowBookmarks(!showBookmarks)}
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Bookmarks"
          >
            <Bookmark className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>

          <button
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Downloads"
          >
            <Download className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>

          <button
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            title="Menu"
          >
            <Menu className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
        </div>
      </div>

      {/* Bookmarks Bar */}
      {showBookmarks && bookmarks.length > 0 && (
        <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
          {bookmarks.map((bookmark) => (
            <button
              key={bookmark.id}
              onClick={() => handleNavigate(bookmark.url)}
              className="flex items-center gap-1 px-3 py-1 text-sm rounded hover:bg-gray-200 dark:hover:bg-gray-700 whitespace-nowrap"
            >
              <img
                src={`https://www.google.com/s2/favicons?domain=${new URL(bookmark.url).hostname}`}
                alt=""
                className="w-4 h-4"
              />
              <span>{bookmark.title}</span>
            </button>
          ))}
        </div>
      )}

      {/* Main Browser Content */}
      <div className="flex-1 relative">
        <BrowserView />
      </div>
    </div>
  );
};

export default App;
