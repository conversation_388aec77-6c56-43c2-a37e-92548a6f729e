{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 16403927197798629762, "deps": [[561782849581144631, "html5ever", false, 1907626582843265068], [3150220818285335163, "url", false, 17738334534580791688], [3334271191048661305, "windows_version", false, 1436286124606938623], [4071963112282141418, "serde_with", false, 2330842558548076710], [4899080583175475170, "semver", false, 9495104336160992446], [5986029879202738730, "log", false, 8296542160911402746], [6262254372177975231, "kuchiki", false, 15735861376896020495], [6606131838865521726, "ctor", false, 11343249673224450539], [6997837210367702832, "infer", false, 4453963977761441640], [8008191657135824715, "thiserror", false, 13516774549906574380], [9689903380558560274, "serde", false, 3711165212804299083], [10301936376833819828, "json_patch", false, 6506959593427395100], [11989259058781683633, "dunce", false, 2758556744653672316], [14132538657330703225, "brotli", false, 9226035117013190863], [15367738274754116744, "serde_json", false, 319892754931724390], [15622660310229662834, "walkdir", false, 10663511346200779593], [15932120279885307830, "memchr", false, 1696798330147534137], [17155886227862585100, "glob", false, 3386706419477064543], [17186037756130803222, "phf", false, 7630139918907499869]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-bd8db7a744212981\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}