{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 1376057193071578599, "deps": [[1573238666360410412, "rand_chacha", false, 13233908901473457053], [18130209639506977569, "rand_core", false, 2882986167817675731]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-cdfa25691712a373\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}