{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 5408242616063297496, "profile": 13390825267576250845, "path": 198532784448658390, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-7fbbbfdf0f902425\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}