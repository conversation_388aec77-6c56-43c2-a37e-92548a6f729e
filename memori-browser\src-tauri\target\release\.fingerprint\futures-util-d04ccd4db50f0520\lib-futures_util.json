{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 15389663814530670383, "deps": [[1615478164327904835, "pin_utils", false, 10967901199649420859], [1906322745568073236, "pin_project_lite", false, 15888961290013833884], [5451793922601807560, "slab", false, 5774840742459626156], [7620660491849607393, "futures_core", false, 7888856330301198808], [10565019901765856648, "futures_macro", false, 1301791594008872831], [16240732885093539806, "futures_task", false, 15774454007784581045]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-d04ccd4db50f0520\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}