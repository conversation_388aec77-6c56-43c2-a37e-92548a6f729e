{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2040997289075261528, "path": 1334397966227272379, "deps": [[9620753569207166497, "zerovec_derive", false, 17090870560417224702], [10706449961930108323, "yoke", false, 15653458860018746378], [17046516144589451410, "zerofrom", false, 4353104755709888273]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerovec-7608b510d4dcec2c\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}