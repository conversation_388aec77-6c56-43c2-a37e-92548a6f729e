import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';

export interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
  createdAt: string;
}

export interface Bookmark {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  createdAt: string;
}

export interface NavigationEvent {
  tabId: string;
  url: string;
  title: string;
  timestamp: string;
}

interface BrowserState {
  // Tab management
  tabs: Tab[];
  activeTabId: string | null;
  
  // Bookmarks
  bookmarks: Bookmark[];
  
  // Navigation state
  isLoading: boolean;
  
  // Actions
  createTab: (url?: string) => Promise<void>;
  closeTab: (tabId: string) => Promise<void>;
  setActiveTab: (tabId: string) => void;
  navigateTab: (tabId: string, url: string) => Promise<void>;
  goBack: (tabId: string) => Promise<void>;
  goForward: (tabId: string) => Promise<void>;
  refreshTab: (tabId: string) => Promise<void>;
  
  // Bookmark actions
  addBookmark: (url: string, title: string) => void;
  removeBookmark: (bookmarkId: string) => void;
  
  // Internal actions
  updateTab: (tabId: string, updates: Partial<Tab>) => void;
  loadTabs: () => Promise<void>;
  setupEventListeners: () => void;
}

export const useBrowserStore = create<BrowserState>((set, get) => ({
  tabs: [],
  activeTabId: null,
  bookmarks: [],
  isLoading: false,

  createTab: async (url = 'https://www.google.com') => {
    try {
      set({ isLoading: true });
      const newTab = await invoke<Tab>('create_tab', { url });
      
      set(state => ({
        tabs: [...state.tabs, newTab],
        activeTabId: newTab.id,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Failed to create tab:', error);
      set({ isLoading: false });
    }
  },

  closeTab: async (tabId: string) => {
    try {
      await invoke('close_tab', { tabId });
      
      set(state => {
        const newTabs = state.tabs.filter(tab => tab.id !== tabId);
        let newActiveTabId = state.activeTabId;
        
        // If we're closing the active tab, switch to another tab
        if (state.activeTabId === tabId) {
          newActiveTabId = newTabs.length > 0 ? newTabs[newTabs.length - 1].id : null;
        }
        
        return {
          tabs: newTabs,
          activeTabId: newActiveTabId,
        };
      });
    } catch (error) {
      console.error('Failed to close tab:', error);
    }
  },

  setActiveTab: (tabId: string) => {
    set({ activeTabId: tabId });
  },

  navigateTab: async (tabId: string, url: string) => {
    try {
      // Update UI immediately for responsiveness
      get().updateTab(tabId, { isLoading: true, url });
      
      await invoke('navigate_to', { tabId, url });
    } catch (error) {
      console.error('Failed to navigate:', error);
      get().updateTab(tabId, { isLoading: false });
    }
  },

  goBack: async (tabId: string) => {
    try {
      const canGoBack = await invoke<boolean>('go_back', { tabId });
      if (canGoBack) {
        get().updateTab(tabId, { isLoading: true });
      }
    } catch (error) {
      console.error('Failed to go back:', error);
    }
  },

  goForward: async (tabId: string) => {
    try {
      const canGoForward = await invoke<boolean>('go_forward', { tabId });
      if (canGoForward) {
        get().updateTab(tabId, { isLoading: true });
      }
    } catch (error) {
      console.error('Failed to go forward:', error);
    }
  },

  refreshTab: async (tabId: string) => {
    try {
      get().updateTab(tabId, { isLoading: true });
      await invoke('refresh_tab', { tabId });
    } catch (error) {
      console.error('Failed to refresh tab:', error);
      get().updateTab(tabId, { isLoading: false });
    }
  },

  addBookmark: (url: string, title: string) => {
    const bookmark: Bookmark = {
      id: crypto.randomUUID(),
      url,
      title,
      createdAt: new Date().toISOString(),
    };
    
    set(state => ({
      bookmarks: [...state.bookmarks, bookmark],
    }));
  },

  removeBookmark: (bookmarkId: string) => {
    set(state => ({
      bookmarks: state.bookmarks.filter(b => b.id !== bookmarkId),
    }));
  },

  updateTab: (tabId: string, updates: Partial<Tab>) => {
    set(state => ({
      tabs: state.tabs.map(tab =>
        tab.id === tabId ? { ...tab, ...updates } : tab
      ),
    }));
  },

  loadTabs: async () => {
    try {
      const tabs = await invoke<Tab[]>('get_all_tabs');
      set({ tabs });
    } catch (error) {
      console.error('Failed to load tabs:', error);
    }
  },

  setupEventListeners: () => {
    // Listen for tab events from Rust backend
    listen<Tab>('tab-created', (event) => {
      const tab = event.payload;
      set(state => ({
        tabs: [...state.tabs, tab],
        activeTabId: tab.id,
      }));
    });

    listen<string>('tab-closed', (event) => {
      const tabId = event.payload;
      set(state => ({
        tabs: state.tabs.filter(tab => tab.id !== tabId),
        activeTabId: state.activeTabId === tabId ? 
          (state.tabs.length > 1 ? state.tabs[0].id : null) : 
          state.activeTabId,
      }));
    });

    listen<NavigationEvent>('navigation-start', (event) => {
      const { tabId, url } = event.payload;
      get().updateTab(tabId, { isLoading: true, url });
    });

    listen<NavigationEvent>('navigation-complete', (event) => {
      const { tabId, url, title } = event.payload;
      get().updateTab(tabId, { 
        isLoading: false, 
        url, 
        title,
        canGoBack: true, // Will be updated with real state
        canGoForward: false,
      });
    });

    listen<NavigationEvent>('navigation-back', (event) => {
      const { tabId, url, title } = event.payload;
      get().updateTab(tabId, { url, title, isLoading: false });
    });

    listen<NavigationEvent>('navigation-forward', (event) => {
      const { tabId, url, title } = event.payload;
      get().updateTab(tabId, { url, title, isLoading: false });
    });

    listen<NavigationEvent>('navigation-refresh', (event) => {
      const { tabId } = event.payload;
      get().updateTab(tabId, { isLoading: true });
    });

    listen<{ tabId: string; title: string }>('tab-title-updated', (event) => {
      const { tabId, title } = event.payload;
      get().updateTab(tabId, { title });
    });
  },
}));

// Initialize event listeners when the store is created
useBrowserStore.getState().setupEventListeners();
